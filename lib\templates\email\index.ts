export const generalEmailHTMLtemplate = ({
  subject,
  receiverName,
  title,
  para1,
  para2,
  cta,
  postScript
}: {
  subject: string
  receiverName: string
  title: string
  para1: string
  para2: string
  cta: {
    url: string
    text: string
  }
  postScript: {
    title: string
    text: string
  }
}) => `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional //EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:v="urn:schemas-microsoft-com:vml" lang="en">
   <head>
      <title>${subject}</title>
      <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <style type="text/css">
         @media only screen and (max-width: 480px) {
         table.blk, table.tblText, .bmeHolder, .bmeHolder1, table.bmeMainColumn {
         width: 100% !important;
         }
         }
         @media only screen and (max-width: 480px) {
         td.blk_container, .blk_parent, .bmeLeftColumn, .bmeRightColumn, .bmeColumn1, .bmeColumn2, .bmeColumn3, .bmeBody {
         display: table !important;
         max-width: 600px !important;
         width: 100% !important;
         }
         }
         @media only screen and (max-width: 480px) {
         table.container-table, .bmeheadertext, .container-table {
         width: 95% !important;
         }
         }
      </style>
   </head>
   <body topmargin="0" leftmargin="0" style="height: 100% !important; margin: 0; padding: 0; width: 100% !important; min-width: 100%">
      <table width="100%" cellspacing="0" cellpadding="0" border="0" name="bmeMainBody" bgcolor="#e8e3ce" style="background-color: #e8e3ce">
         <tbody>
            <tr>
               <td width="100%" valign="top" align="center">
                  <table cellspacing="0" cellpadding="0" border="0" name="bmeMainColumnParentTable">
                     <tbody>
                        <tr>
                           <td name="bmeMainColumnParent" style="border: 0px none transparent; border-radius: 0px; border-collapse: separate">
                              <table name="bmeMainColumn" class="bmeHolder bmeMainColumn" style="max-width: 600px; overflow: visible; border-radius: 0px; border-collapse: separate; border-spacing: 0px;" cellspacing="0" cellpadding="0" border="0" align="center">
                                 <tbody>
                                    <tr>
                                       <td width="100%" class="bmeHolder" valign="top" align="center" name="bmeMainContentParent" style="border: 1px solid rgb(224, 224, 224); border-radius: 5px; border-collapse: separate; border-spacing: 0px; overflow: hidden;">
                                          <table name="bmeMainContent" style="border-radius: 0px; border-collapse: separate; border-spacing: 0px; border: 0px none transparent;" width="100%" cellspacing="0" cellpadding="0" border="0" align="center">
                                             <tbody>
                                                <!-- Header Section -->
                                                <tr>
                                                   <td width="100%" class="blk_container bmeHolder" name="bmeHeader" valign="top" align="center" style="color: rgb(56, 56, 56); border-bottom: 1px solid #d6d0b8; background-color: #f5f2e8;" bgcolor="#f5f2e8">
                                                      <div class="blk_wrapper">
                                                         <table width="600" cellspacing="0" cellpadding="0" border="0" class="blk" name="blk_divider">
                                                            <tbody>
                                                               <tr>
                                                                  <td class="tblCellMain" style="padding: 0px">
                                                                     <table class="tblLine" cellspacing="0" cellpadding="0" border="0" width="100%" style="border-top-width: 10px; border-top-color: #bf996f; border-top-style: solid; min-width: 1px;">
                                                                        <tbody>
                                                                           <tr>
                                                                              <td><span></span></td>
                                                                           </tr>
                                                                        </tbody>
                                                                     </table>
                                                                  </td>
                                                               </tr>
                                                            </tbody>
                                                         </table>
                                                      </div>
                                                      <div class="blk_wrapper">
                                                         <table width="600" cellspacing="0" cellpadding="0" border="0" class="blk" name="blk_boxtext">
                                                            <tbody>
                                                               <tr>
                                                                  <td align="center" name="bmeBoxContainer" style="padding: 20px;">
                                                                     <table cellspacing="0" cellpadding="0" width="100%" name="tblText" border="0" class="tblText">
                                                                        <tbody>
                                                                           <tr>
                                                                              <td valign="top" align="left" style="padding: 20px; font-family: Arial, Helvetica, sans-serif; font-weight: normal; font-size: 14px; color: rgb(56, 56, 56); background-color: rgba(0, 0, 0, 0); border-collapse: collapse; word-break: break-word;" name="tblCell" class="tblCell">
                                                                                 <div style="line-height: 170%; text-align: center">
                                                                                    <span style="font-size: 24px; font-family: 'Arial Black', 'Arial Bold', sans-serif; color: #bf996f; line-height: 170%;"><strong>${title}</strong></span>
                                                                                 </div>
                                                                              </td>
                                                                           </tr>
                                                                        </tbody>
                                                                     </table>
                                                                  </td>
                                                               </tr>
                                                            </tbody>
                                                         </table>
                                                      </div>
                                                   </td>
                                                </tr>
                                                <!-- Body Section -->
                                                <tr>
                                                   <td width="100%" class="blk_container bmeHolder bmeBody" name="bmeBody" valign="top" align="center" style="color: rgb(56, 56, 56); border: 0px none transparent; background-color: rgb(255, 255, 255);" bgcolor="#ffffff">
                                                      <div class="blk_wrapper">
                                                         <table width="600" cellspacing="0" cellpadding="0" border="0" class="blk" name="blk_boxtext">
                                                            <tbody>
                                                               <tr>
                                                                  <td align="center" name="bmeBoxContainer" style="padding: 20px;">
                                                                     <table cellspacing="0" cellpadding="0" width="100%" name="tblText" border="0" class="tblText">
                                                                        <tbody>
                                                                           <tr>
                                                                              <td valign="top" align="left" style="padding: 20px; font-family: Arial, Helvetica, sans-serif; font-weight: normal; font-size: 14px; color: rgb(56, 56, 56); background-color: rgba(0, 0, 0, 0); border-collapse: collapse; word-break: break-word;" name="tblCell" class="tblCell">
                                                                                 <div style="line-height: 170%; text-align: left">
                                                                                    <span style="font-size: 16px; font-family: Helvetica, Arial, sans-serif; color: black; line-height: 170%;"><strong>Hi ${receiverName},</strong></span>
                                                                                    <br /><br />
                                                                                    <span style="font-size: 14px; font-family: Helvetica, Arial, sans-serif; color: #92400e; line-height: 170%;">
                                                                                    ${para1}
                                                                                    </span>
                                                                                 </div>
                                                                              </td>
                                                                           </tr>
                                                                        </tbody>
                                                                     </table>
                                                                  </td>
                                                               </tr>
                                                            </tbody>
                                                         </table>
                                                      </div>
                                                      <!-- CTA Button -->
                                                      <div class="blk_wrapper">
                                                         <table width="600" cellspacing="0" cellpadding="0" border="0" class="blk" name="blk_button">
                                                            <tbody>
                                                               <tr>
                                                                  <td width="20"></td>
                                                                  <td align="center">
                                                                     <table class="tblContainer" cellspacing="0" cellpadding="0" border="0" width="100%">
                                                                        <tbody>
                                                                           <tr>
                                                                              <td height="10"></td>
                                                                           </tr>
                                                                           <tr>
                                                                              <td align="center">
                                                                                 <table cellspacing="0" cellpadding="0" border="0" class="bmeButton" style="border-collapse: separate">
                                                                                    <tbody>
                                                                                       <tr>
                                                                                          <td style="border-radius: 8px; border: 0px none transparent; text-align: center; background-color: #bf996f;" class="bmeButtonText">
                                                                                             <a href="${cta.url}" style="color: #ffffff; text-decoration: none; cursor: pointer; display: inline-block; padding: 16px 32px; font-family: Helvetica, Arial, sans-serif; font-size: 16px; font-weight: bold; border-radius: 8px;" target="_blank">
                                                                                             ${cta.text}
                                                                                             </a>
                                                                                          </td>
                                                                                       </tr>
                                                                                    </tbody>
                                                                                 </table>
                                                                              </td>
                                                                           </tr>
                                                                           <tr>
                                                                              <td height="20"></td>
                                                                           </tr>
                                                                        </tbody>
                                                                     </table>
                                                                  </td>
                                                                  <td width="20"></td>
                                                               </tr>
                                                            </tbody>
                                                         </table>
                                                      </div>
                                                      <!-- Closing Message -->
                                                      <div class="blk_wrapper">
                                                         <table width="600" cellspacing="0" cellpadding="0" border="0" class="blk" name="blk_boxtext">
                                                            <tbody>
                                                               <tr>
                                                                  <td align="center" name="bmeBoxContainer" style="padding: 20px;">
                                                                     <table cellspacing="0" cellpadding="0" width="100%" name="tblText" border="0" class="tblText">
                                                                        <tbody>
                                                                           <tr>
                                                                              <td valign="top" align="left" style="padding: 20px; font-family: Arial, Helvetica, sans-serif; font-weight: normal; font-size: 14px; color: rgb(56, 56, 56); background-color: rgba(0, 0, 0, 0); border-collapse: collapse; word-break: break-word;" name="tblCell" class="tblCell">
                                                                                 <div style="line-height: 170%; text-align: left">
                                                                                    <span style="font-size: 14px; font-family: Helvetica, Arial, sans-serif; color: #92400e; line-height: 170%;">
                                                                                    ${para2}
                                                                                    <br /><br />
                                                                                    Thank you for using Smart Counsel AI.
                                                                                    <br /><br />
                                                                                    Best regards,<br />
                                                                                    <strong style="color: #bf996f;">Smart Counsel AI Team</strong>
                                                                                    </span>
                                                                                 </div>
                                                                              </td>
                                                                           </tr>
                                                                        </tbody>
                                                                     </table>
                                                                  </td>
                                                               </tr>
                                                            </tbody>
                                                         </table>
                                                      </div>
                                                      <!-- Support Notice -->
                                                      <div class="blk_wrapper">
                                                         <table width="600" cellspacing="0" cellpadding="0" border="0" class="blk" name="blk_boxtext">
                                                            <tbody>
                                                               <tr>
                                                                  <td align="center" name="bmeBoxContainer" style="padding: 20px;">
                                                                     <table cellspacing="0" cellpadding="0" width="100%" name="tblText" border="0" class="tblText">
                                                                        <tbody>
                                                                           <tr>
                                                                              <td valign="top" align="left" style="padding: 20px; font-family: Arial, Helvetica, sans-serif; font-weight: normal; font-size: 14px; color: rgb(56, 56, 56); border: 2px solid #bf996f; background-color: #f5f2e8; border-collapse: collapse; word-break: break-word;" name="tblCell" class="tblCell">
                                                                                 <div style="line-height: 170%">
                                                                                    <span style="font-family: Helvetica, Arial, sans-serif; font-size: 14px; color: #92400e; line-height: 170%;">
                                                                                    <strong style="color: #bf996f;">${postScript.title}</strong><br />
                                                                                    ${postScript.text}
                                                                                    </span>
                                                                                 </div>
                                                                              </td>
                                                                           </tr>
                                                                        </tbody>
                                                                     </table>
                                                                  </td>
                                                               </tr>
                                                            </tbody>
                                                         </table>
                                                      </div>
                                                   </td>
                                                </tr>
                                             </tbody>
                                          </table>
                                       </td>
                                    </tr>
                                 </tbody>
                              </table>
                           </td>
                        </tr>
                     </tbody>
                  </table>
               </td>
            </tr>
         </tbody>
      </table>
   </body>
</html>`
