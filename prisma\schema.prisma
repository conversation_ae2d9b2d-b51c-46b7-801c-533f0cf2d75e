generator masterdb_client {
  provider = "prisma-client-js"
}

datasource masterdb {
  provider     = "mysql"
  url          = env("DATABASE_URL")
  relationMode = "prisma"
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @masterdb.Text
  access_token      String? @masterdb.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @masterdb.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
}

enum Region {
  US
  IN
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model User {
  id            String    @id @default(cuid())
  teamId        String?
  name          String?
  email         String?   @unique
  passwordHash  String?
  emailVerified DateTime?
  image         String?
  userType      Role      @default(user)
  region        Region    @default(IN)
  createdAt     DateTime  @default(now())

  team               Team?                @relation(fields: [teamId], references: [id], onDelete: Cascade)
  accounts           Account[]
  sessions           Session[]
  userSettings       UserSettings?
  ResetPasswordToken ResetPasswordToken[]
  Newsletter         Newsletter[]
  Binder             Binder[]
  CaseFiles          CaseFile[]

  @@index([teamId])
}

model UserSettings {
  id                 String   @id @default(cuid())
  userId             String   @unique
  questionAssessment Boolean  @default(true)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @default(now()) @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

enum Role {
  admin
  super
  user
}

enum TeamPlan {
  trial
  basic
  premium
}

model Team {
  id        String   @id @default(cuid())
  name      String
  plan      TeamPlan @default(trial)
  createdAt DateTime @default(now())

  User               User[]
  TeamDocument       TeamDocument[]
  TeamPeriodicCredit TeamPeriodicCredit[]
  TeamCreditUsed     TeamCreditUsed[]
  ShowCauseNotice    ShowCauseNotice[]
  Binder             Binder[]
  LLMTokenUsage      LLMTokenUsage[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Dataset {
  id          Int      @id @default(autoincrement())
  name        String   @masterdb.VarChar(255)
  description String?  @masterdb.Text
  binderId    String?
  createdBy   String   @default("system")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt

  DocumentRecordDatasetMap DocumentRecordDatasetMap[]
  Binder                   Binder?                    @relation(fields: [binderId], references: [id])

  @@index([createdBy])
  @@index([binderId])
}

model DocumentRecords {
  id           Int       @id @default(autoincrement())
  source       String
  ref          String
  title        String    @masterdb.VarChar(1000)
  meta         String    @masterdb.Text
  url          String    @masterdb.VarChar(500)
  date         DateTime?
  html         String    @masterdb.LongText
  content      String    @masterdb.LongText
  summary      String?   @masterdb.Text
  region       Region    @default(US)
  uploadedBy   String    @default("system")
  html_cleaned Boolean   @default(false)
  meta_ready   Boolean   @default(false)
  indexed      Boolean   @default(false)
  binderId     String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @default(now()) @updatedAt

  DocumentResource         DocumentResource[]
  DocumentRecordDatasetMap DocumentRecordDatasetMap[]
  Binder                   Binder?                    @relation(fields: [binderId], references: [id])
  DocumentEvent            DocumentEvent[]

  @@unique([source, ref])
  @@index([source, indexed])
  @@index([html_cleaned, meta_ready])
  @@index([binderId])
}

model DocumentRecordDatasetMap {
  id         Int      @id @default(autoincrement())
  documentId Int
  datasetId  Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  DocumentRecords DocumentRecords @relation(fields: [documentId], references: [id], onDelete: Cascade)
  Dataset         Dataset         @relation(fields: [datasetId], references: [id], onDelete: Cascade)

  @@unique([documentId, datasetId])
  @@index([datasetId])
}

model DocumentResource {
  id               String          @id @default(cuid())
  s3Key            String          @masterdb.VarChar(500)
  fileUrl          String          @masterdb.VarChar(2500)
  documentRecordId Int
  documentRecord   DocumentRecords @relation(fields: [documentRecordId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([documentRecordId])
}

model Newsletter {
  id        String   @id @default(cuid())
  title     String
  slug      String   @unique
  content   Json?
  region    Region   @default(IN)
  published Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())
  userId    String

  user User @relation(fields: [userId], references: [id])

  @@index([userId])
}

model TeamDocument {
  id        String   @id @default(cuid())
  teamId    String
  source    String
  title     String?  @masterdb.VarChar(100)
  region    Region   @default(IN)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  TeamDocumentChunks TeamDocumentChunk[]

  @@index([teamId])
}

model TeamDocumentChunk {
  id             Int      @id @default(autoincrement())
  teamDocumentId String
  content        String   @masterdb.Text
  review         String?  @masterdb.Text
  indexed        Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  teamDocument TeamDocument @relation(fields: [teamDocumentId], references: [id], onDelete: Cascade)

  @@index([teamDocumentId])
}

enum ResearchType {
  private
  case
  law
}

model ResearchStore {
  id             String           @id @default(cuid())
  userId         String
  question       String?          @masterdb.VarChar(1000)
  content        Json
  region         Region           @default(IN)
  type           ResearchType     @default(law)
  source         String           @default("global")
  binderId       String?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  TeamCreditUsed TeamCreditUsed[]

  responseFeedback ResponseFeedback[]
  Binder           Binder?            @relation(fields: [binderId], references: [id])

  @@index([userId, type])
  @@index([binderId])
}

model Binder {
  id        String   @id @default(cuid())
  name      String
  data      Json?
  teamId    String
  createdBy String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  team    Team @relation(fields: [teamId], references: [id], onDelete: Cascade)
  creator User @relation(fields: [createdBy], references: [id])

  researches          ResearchStore[]
  documents           DocumentRecords[]
  Dataset             Dataset[]
  CaseFiles           CaseFile[]
  DocumentEvent       DocumentEvent[]
  ProcessedChronology ProcessedChronology[]

  @@index([teamId])
  @@index([createdBy])
}

enum CaseFileType {
  PLAINTIFF_INFO
  MEDICAL_CHRONOLOGY
  MEDICAL_CHRONOLOGY_PRO
  INCIDENT_DETAILS
  EVENTS
  SOURCE_LINKS
  CASE_GAPS
  ATTORNEY_STRATEGY_PLAN
  CASE_EVALUATION
  REVISED_CASE_EVALUATION
  LIABILITY_ASSESSMENT
  ECONOMIC_DAMAGES
  NON_ECONOMIC_DAMAGES
  PUNITIVE_DAMAGES
  DAMAGES_CALCULATION
  RISK_ASSESSMENT
  LITIGATION_STRATEGY
  DEFENSE_PERSPECTIVE
  EXTRACTED_FINANCIAL_DATA
  DEMAND_LETTER
  DAMAGES_OVERVIEW_DETAILED
  PAST_MEDICAL_EXPENSES_DETAILED
  FUTURE_MEDICAL_EXPENSES_DETAILED
  LOSS_OF_INCOME_DETAILED
  LOSS_OF_HOUSEHOLD_SERVICES_DETAILED
  PAIN_AND_SUFFERING_DETAILED
  PUNITIVE_DAMAGES_DETAILED
}

model CaseFile {
  id                      String       @id @default(cuid())
  binderId                String
  creatorId               String
  fileType                CaseFileType
  content                 String?      @masterdb.LongText
  selectedDocumentsByType Json?
  queueId                 String?
  createdAt               DateTime     @default(now())
  updatedAt               DateTime     @updatedAt

  creator User   @relation(fields: [creatorId], references: [id])
  binder  Binder @relation(fields: [binderId], references: [id])

  @@unique([binderId, fileType])
  @@index([creatorId])
}

model ResponseFeedback {
  id              String       @id @default(cuid())
  type            FeedbackType
  researchId      String
  messageId       String
  feedbackContent String?      @masterdb.Text
  createdAt       DateTime     @default(now())

  researchStore ResearchStore @relation(fields: [researchId], references: [id], onDelete: Cascade)

  @@index([researchId])
}

model TeamPeriodicCredit {
  id              String     @id @default(cuid())
  teamId          String
  type            CreditType
  period          Int
  creditAvailable Int        @default(0)
  expiresAt       DateTime
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@index([teamId, type, expiresAt])
}

model TeamCreditUsed {
  id         Int        @id @default(autoincrement())
  teamId     String
  type       CreditType
  refId      String
  eventId    String
  creditUsed Int        @default(1)
  createdAt  DateTime   @default(now())

  team     Team          @relation(fields: [teamId], references: [id], onDelete: Cascade)
  research ResearchStore @relation(fields: [refId], references: [id], onDelete: Cascade)

  @@index([teamId, refId, createdAt])
  @@index([refId])
}

enum CreditType {
  research
  document
  team
  case
}

model ResetPasswordToken {
  id        Int      @id @default(autoincrement())
  userId    String
  token     String   @default(cuid())
  expires   DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@index([userId])
}

model ShowCauseNotice {
  id                        String                @id @default(cuid())
  title                     String
  showCauseNoticeSummary    String                @masterdb.Text
  showCauseReplySummary     String                @masterdb.Text
  showCauseJudgementSummary String?               @masterdb.Text
  orderLength               Int                   @default(3)
  processedLength           Int                   @default(0)
  caseRecords               String?               @masterdb.Text
  finalJudgementSummary     String?               @masterdb.Text
  teamId                    String
  status                    ShowCauseNoticeStatus @default(uploaded)
  createdAt                 DateTime              @default(now())
  updatedAt                 DateTime              @updatedAt

  team Team @relation(fields: [teamId], references: [id])

  @@index([teamId])
}

enum ShowCauseNoticeStatus {
  uploaded
  summerized
  prepared
  published
}

model OpenAiBatchProcess {
  id                  Int      @id @default(autoincrement())
  type                String
  payload             String   @masterdb.Text
  inputFileId         String
  batchId             String?
  outputFileId        String?
  status              String   @default("pending")
  uploadResponse      String?  @masterdb.Text
  batchStatusResponse String?  @masterdb.Text
  createdAt           DateTime @default(now())
}

model LinkRecords {
  id            Int           @id @default(autoincrement())
  court         String
  source        String
  url           String
  parentId      Int? // base links won't have a parent
  done          Boolean       @default(false)
  processing    Boolean       @default(false)
  parent        LinkRecords?  @relation("ParentLink", fields: [parentId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subLinks      LinkRecords[] @relation("ParentLink")
  duration      Int           @default(0)
  subLinksCount Int           @default(0)
  type          LinkType      @default(LINKS)
  state         String        @default("")
  linkType      LinksType     @default(LAW)
  createdAt     DateTime      @default(now())

  @@index([source, state, linkType])
  @@index([parentId])
}

model Prompt {
  id             String   @id @default(cuid())
  source         String
  role           String   @default("SYSTEM")
  prompt         String   @masterdb.Text
  expectedOutput String?  @masterdb.Text
  variables      Json?
  version        Int      @default(1)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @default(now()) @updatedAt

  history PromptsHistory[]
}

model PromptsHistory {
  id             Int      @id @default(autoincrement())
  refId          String
  source         String   @default("SYSTEM")
  role           String
  prompt         String   @masterdb.Text
  expectedOutput String?  @masterdb.Text
  variables      Json // Store as JSON
  createdAt      DateTime @default(now())

  promptRef Prompt @relation(fields: [refId], references: [id], onDelete: Cascade)

  @@index([refId])
}

model QueuedEventProcess {
  id        String            @id @default(cuid())
  type      String
  payload   String            @masterdb.Text
  status    QueuedEventStatus @default(pending)
  response  String?           @masterdb.Text
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  @@index([type, status])
}

enum QueuedEventStatus {
  pending
  processing
  completed
  failed
}

enum LinkType {
  LINKS
  DATA
}

enum LinksType {
  LAW
  REGULATION
}

enum FeedbackType {
  POSITIVE
  NEGATIVE
}

model DocumentEvent {
  id               String   @id @default(cuid())
  binderId         String
  documentId       Int
  pageRange        String // e.g., "1-5" or "23"
  event            String   @masterdb.VarChar(500)
  eventType        String   @masterdb.VarChar(100)
  eventDescription String   @masterdb.Text
  timestamp        DateTime
  estimatedTime    Boolean  @default(true) // true if time was estimated by LLM
  rawExtractedData Json? // store original extraction for debugging
  processed        Boolean  @default(false) // for deduplication tracking
  relevanceScore   Float?   @default(0.0) // 0.0-1.0 legal relevance for filtering top events
  createdAt        DateTime @default(now())

  binder   Binder          @relation(fields: [binderId], references: [id], onDelete: Cascade)
  document DocumentRecords @relation(fields: [documentId], references: [id], onDelete: Cascade)

  @@index([binderId, timestamp])
  @@index([documentId])
  @@index([processed])
  @@index([binderId, relevanceScore])
}

model ProcessedChronology {
  id          String       @id @default(cuid())
  binderId    String
  fileType    CaseFileType
  eventIds    Json // array of DocumentEvent IDs used
  content     String       @masterdb.LongText
  metadata    Json? // store processing metadata
  generatedAt DateTime     @default(now())

  binder Binder @relation(fields: [binderId], references: [id], onDelete: Cascade)

  @@unique([binderId, fileType])
  @@index([binderId])
}

enum LLMProvider {
  OPENAI
  GEMINI
  ANTHROPIC
  AZURE
}

model LLMTokenUsage {
  id       String      @id @default(cuid())
  teamId   String
  provider LLMProvider
  model    String
  purpose  String // e.g., "research", "document_processing", "case_analysis"
  activity String // e.g., "completion", "chat", "embedding"

  // Token counts - using nullable fields to accommodate different provider structures
  promptTokens     Int?
  completionTokens Int?
  totalTokens      Int?

  // OpenAI specific details
  cachedTokens    Int?
  audioTokens     Int?
  reasoningTokens Int?

  // Gemini specific details
  candidatesTokenCount Int?
  cachedContentTokens  Int?
  toolUsePromptTokens  Int?
  thoughtsTokenCount   Int?

  // Additional metadata
  requestId    String? // OpenAI completion ID or Gemini response ID
  modelVersion String? // Version of the model used
  finishReason String? // How the completion ended
  rawUsageData Json? // Store complete raw usage object for debugging

  createdAt DateTime @default(now())

  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)

  @@index([teamId, provider, createdAt])
  @@index([purpose, activity])
  @@index([provider, model])
}
