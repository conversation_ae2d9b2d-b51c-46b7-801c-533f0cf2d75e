# Slack Notifications for Case Events

This document outlines the implementation of Slack notifications for case-related events in the LexLumen application.

## Overview

The system now sends real-time notifications to the `user-activities` Slack channel for all case-related processes including:
- Medical Chronology (med-cron)
- Case Evaluation (case-eval)
- Case Evaluation Lite (case-eval-lite)
- Demand Letter (demand-letter)

## Notification Types

### 1. Process Initiation
Sent when a user starts any case process.
- **Trigger**: User initiates a case process
- **Information**: User details, binder ID, document count, queue ID (if applicable)
- **Icon**: 🚀

### 2. Processing Started
Sent when the cron job begins processing a queued item (currently only for medical chronology).
- **Trigger**: Cron job starts processing
- **Information**: User, binder ID, process ID
- **Icon**: ⚡

### 3. Process Completed
Sent when a case process finishes successfully.
- **Trigger**: Successful completion
- **Information**: User, binder ID, process ID, duration
- **Icon**: ✅

### 4. Process Failed
Sent when a case process encounters an error.
- **Trigger**: Process failure/exception
- **Information**: User, binder ID, process ID, duration, error message
- **Icon**: 🚨

## Implementation Details

### Files Modified

1. **`lib/services/case-notifications.ts`** (NEW)
   - Core notification service with functions for each notification type
   - Integrates with existing `slack-service.ts`
   - Provides helper functions for data formatting

2. **`lib/utils/queue-event-types.ts`**
   - Added `CASE_EVALUATION` and `DEMAND_LETTER` event types

3. **`lib/actions/case/med-cron.ts`**
   - Added initiation notification in `requestMedicalChronologyGeneration`
   - Added processing start, completion, and failure notifications in `handleMedicalChronologyGeneration`

4. **`lib/actions/case/case-eval.ts`**
   - Added initiation, completion, and failure notifications

5. **`lib/actions/case/case-eval-lite.ts`**
   - Added initiation, completion, and failure notifications

6. **`lib/actions/case/demand-letter.ts`**
   - Added initiation, completion, and failure notifications

7. **`pages/api/cron/case-processor.ts`**
   - Updated to handle all case event types (future-proofing)
   - Added support for case evaluation and demand letter events

## Message Format

Each notification includes:
- **Event Type**: Medical Chronology, Case Evaluation, or Demand Letter
- **Binder ID**: For traceability
- **User Information**: Name and email
- **Process/Queue ID**: For technical tracking
- **Timestamp**: When the event occurred
- **Duration**: How long the process took (for completion/failure)
- **Error Details**: Truncated error message (for failures)
- **Document Count**: Number of documents processed (for initiation)

## Configuration

### Environment Variables
- `SLACK_TOKEN`: Required for Slack API access (already configured)

### Channel
- **Channel Name**: `user-activities`
- **Purpose**: Track all user-initiated case processing activities

## Usage Examples

### Medical Chronology Flow
1. User initiates → 🚀 "Medical Chronology Generation Initiated"
2. Cron starts → ⚡ "Medical Chronology Processing Started" 
3. Success → ✅ "Medical Chronology Generation Completed"

### Case Evaluation Flow (Direct Processing)
1. User initiates → 🚀 "Case Evaluation Generation Initiated"
2. Success → ✅ "Case Evaluation Generation Completed"

### Error Handling
If any process fails → 🚨 "Process Generation Failed" with error details

## Benefits

1. **Real-time Monitoring**: Track all case processing activities as they happen
2. **Error Visibility**: Immediate notification of failures with error details
3. **Performance Tracking**: Duration information for optimization insights
4. **User Activity Tracking**: Monitor which users are actively using case features
5. **Debugging Support**: Process IDs and queue IDs for technical troubleshooting

## Future Enhancements

- Add notification preferences per user/team
- Include more detailed metrics (tokens used, file sizes, etc.)
- Add notification for document processing stages
- Implement notification batching for high-volume periods 