'use client'

import { useState, useCallback, useMemo, useEffect, useRef } from 'react'
import { CaseDocumentSelector } from './doc-upload-case-selector'
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import ReviewAndFinalizeUploads from './review-finalize-uploads'
import DocumentGeneration from './doc-generation'
import { ArrowRightCircle, Loader2, Save } from 'lucide-react'
import {
  requestMedicalChronologyGeneration,
  saveCaseFileContent,
  checkMedicalChronologyStatus
} from '@/lib/actions/case/med-cron'
import { toast } from '@/components/ui/use-toast'
import { Binder, CaseFile } from '@prisma/client'
import { CopyButton, DownloadButton } from '../document/document-actions'
import { useRouter } from 'next/navigation'
import { TipTapEditor } from '@/components/external/tiptap/tiptap-editor'
import { DocumentTitle } from '@/types/case'
import { useDocumentPolling } from '@/hooks/use-document-polling'

import { calculateEstimatedStepPosition } from '@/lib/utils'
import {
  MedicalChronologyStrategyForm,
  type MedicalChronologyStrategyFormData,
  type MedicalChronologyStrategyFormRef
} from '../forms/medical-chronology-strategy-form'

type DocType =
  | 'Police/Incident Reports'
  | 'EMS Reports'
  | 'Hospital Records'
  | 'Treatment Records'
  | 'Imaging Reports'
  | 'Medical Bills'
  | 'Insurance Correspondence'
  | 'Witness Statements'

type SelectedDocuments = Record<DocType | string, string[]>

// Constants to avoid repetition
const DOCUMENT_TYPES: DocType[] = [
  'Police/Incident Reports',
  'EMS Reports',
  'Hospital Records',
  'Treatment Records',
  'Imaging Reports',
  'Medical Bills',
  'Insurance Correspondence',
  'Witness Statements'
]

const STEPS = {
  SELECT: 'step1',
  ADDITIONAL: 'step2',
  STRATEGY: 'step3',
  REVIEW: 'step4',
  GENERATE: 'step5'
} as const

export default function MedicalChronologyDocSelector({
  binder,
  allDocuments: initialDocuments,
  medicalChronology,
  usageStats = {
    available: 0,
    used: 0
  }
}: {
  binder: Binder
  allDocuments: DocumentTitle[]
  medicalChronology: CaseFile | null
  usageStats?: {
    available: number
    used: number
  }
}) {
  // Use the polling hook to get fresh documents
  const {
    documents: allDocuments,
    isPolling,
    updateDocuments
  } = useDocumentPolling(binder.id, initialDocuments)

  // Parse saved document selections from medicalChronology
  const selectedDocumentsByTypeFromStore =
    (medicalChronology?.selectedDocumentsByType || {}) as SelectedDocuments

  const router = useRouter()

  // Initialize state with properly typed structure
  const [selectedDocumentsByType, setSelectedDocumentsByType] =
    useState<SelectedDocuments>(() => {
      // Initialize with default empty arrays for each document type
      const initial = DOCUMENT_TYPES.reduce((acc, type) => {
        acc[type] = selectedDocumentsByTypeFromStore[type] || []
        return acc
      }, {} as SelectedDocuments)

      // Add additional documents category
      initial['Additional Documents'] =
        selectedDocumentsByTypeFromStore['Additional Documents'] || []
      return initial
    })

  // Track current step and generation state
  const [currentTab, setCurrentTab] = useState<
    (typeof STEPS)[keyof typeof STEPS]
  >(medicalChronology?.updatedAt ? STEPS.GENERATE : STEPS.SELECT)
  const [step5Allowed, setStep5Allowed] = useState(
    !!medicalChronology?.content && !!medicalChronology?.queueId
  )
  const [generatedDocument, setGeneratedDocument] = useState<string | null>(
    medicalChronology?.content || null
  )

  const [isGenerating, setIsGenerating] = useState<boolean>(
    !!medicalChronology?.queueId
  )
  const [isSaving, setIsSaving] = useState<boolean>(
    !!medicalChronology?.queueId
  )
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [editedDocument, setEditedDocument] = useState<string | null>(null)
  const [lastUpdatedAt, setLastUpdatedAt] = useState<Date | null>(
    medicalChronology?.updatedAt || null
  )

  const [strategyInputs, setStrategyInputs] = useState<
    Partial<MedicalChronologyStrategyFormData>
  >({})
  const strategyFormRef = useRef<MedicalChronologyStrategyFormRef>(null)

  // Debug wrapper for setStrategyInputs
  const handleStrategyInputsChange = useCallback(
    (values: Partial<MedicalChronologyStrategyFormData>) => {
      setStrategyInputs(values)
    },
    []
  )

  // Polling effect for medical chronology generation
  useEffect(() => {
    if (!isGenerating || !binder.id) return

    const pollInterval = setInterval(async () => {
      try {
        const result = await checkMedicalChronologyStatus(
          binder.id,
          lastUpdatedAt || undefined
        )

        if (result.success && result.isUpdated && result.content) {
          setGeneratedDocument(result.content)
          setIsGenerating(false)
          setLastUpdatedAt(result.updatedAt ?? null)

          toast({
            title: 'Medical chronology generated successfully',
            description: 'Your medical chronology is ready for review'
          })

          // Clear the interval since generation is complete
          clearInterval(pollInterval)
        }
      } catch (error) {
        console.error('Error polling medical chronology status:', error)
      }
    }, 12000) // Poll every 12 seconds

    return () => clearInterval(pollInterval)
  }, [isGenerating, binder.id, lastUpdatedAt])

  // Memoized calculation for available documents in step 2
  const availableAdditionalDocuments = useMemo(() => {
    // Get all documents already selected in previous steps
    const selectedIdsToExclude = Object.entries(selectedDocumentsByType)
      .filter(([category]) => category !== 'Additional Documents')
      .flatMap(([_, ids]) => ids)

    // Return only documents not already selected
    return allDocuments.filter(
      (doc) => !selectedIdsToExclude.includes(String(doc.id))
    )
  }, [allDocuments, selectedDocumentsByType])

  // Navigation functions
  const goToNextTab = useCallback(async () => {
    // If we're on strategy step, validate and submit form first
    if (currentTab === STEPS.STRATEGY && strategyFormRef.current) {
      const isValid = await strategyFormRef.current.submitForm()
      if (!isValid) {
        toast({
          title: 'Please complete the strategy form',
          description: 'Fix any validation errors before proceeding',
          variant: 'destructive'
        })
        return
      }
    }

    if (currentTab === STEPS.SELECT) setCurrentTab(STEPS.ADDITIONAL)
    else if (currentTab === STEPS.ADDITIONAL) setCurrentTab(STEPS.STRATEGY)
    else if (currentTab === STEPS.STRATEGY) setCurrentTab(STEPS.REVIEW)
  }, [currentTab])

  const goToPrevTab = useCallback(() => {
    if (currentTab === STEPS.REVIEW) setCurrentTab(STEPS.STRATEGY)
    else if (currentTab === STEPS.STRATEGY) setCurrentTab(STEPS.ADDITIONAL)
    else if (currentTab === STEPS.ADDITIONAL) setCurrentTab(STEPS.SELECT)
  }, [currentTab])

  // Tab change handler with validation
  const handleTabChange = useCallback(
    (value: string) => {
      // Prevent navigation during generation or to restricted tabs
      if (isGenerating) return
      if (value === STEPS.GENERATE && !step5Allowed) return
      setCurrentTab(value as (typeof STEPS)[keyof typeof STEPS])
    },
    [isGenerating, step5Allowed]
  )

  // Validation check - ensure at least one required doc type is selected
  const areDocsProvided = useMemo(() => {
    return Object.values(selectedDocumentsByType).some(
      (docs) => docs.length > 0
    )
  }, [selectedDocumentsByType])

  // Handler for document generation
  const handleFinalizeUploads = useCallback(async () => {
    // Prevent multiple submissions
    if (isGenerating) return

    // Update UI state for generation process
    setGeneratedDocument(null)
    setIsGenerating(true)
    setStep5Allowed(true)
    setCurrentTab(STEPS.GENERATE)

    try {
      const result = await requestMedicalChronologyGeneration(
        binder.id,
        selectedDocumentsByType,
        strategyInputs
      )

      if (result.success) {
        // Update lastUpdatedAt to current time for polling comparison
        setLastUpdatedAt(new Date())

        toast({
          title: 'Medical chronology is being generated',
          description: 'You will be notified when the process is complete'
        })
      } else {
        throw new Error('Failed to generate medical chronology')
      }
    } catch (error) {
      // Reset UI state on error
      setStep5Allowed(false)
      setCurrentTab(STEPS.REVIEW)
      setGeneratedDocument(null)

      console.error('Error generating medical chronology:', error)
      toast({
        title: 'Failed to generate medical chronology',
        description:
          'An error occurred while generating the medical chronology',
        variant: 'destructive'
      })
      // } finally {
      //   setIsGenerating(false)
    }
  }, [binder.id, isGenerating, selectedDocumentsByType, strategyInputs])

  // Handle saving changes to the generated document
  const handleSaveChanges = useCallback(async () => {
    if (!medicalChronology?.id || !editedDocument) return

    setIsSaving(true)
    try {
      const result = await saveCaseFileContent(
        medicalChronology.id,
        editedDocument
      )

      if (result.success) {
        setGeneratedDocument(editedDocument)
        setHasUnsavedChanges(false)
        router.refresh()
        toast({
          title: 'Changes saved successfully',
          description: 'Your medical chronology has been updated'
        })
      } else {
        throw new Error('Failed to save changes')
      }
    } catch (error) {
      console.error('Error saving medical chronology:', error)
      toast({
        title: 'Failed to save changes',
        description: 'An error occurred while saving your changes',
        variant: 'destructive'
      })
    } finally {
      setIsSaving(false)
    }
  }, [medicalChronology?.id, editedDocument, router])

  // Handle content changes from the editor
  const handleContentChange = useCallback(
    (newContent: string) => {
      setEditedDocument(newContent)
      setHasUnsavedChanges(newContent !== generatedDocument)
    },
    [generatedDocument]
  )

  //for manual refresh
  // const [isRefreshing, setIsRefreshing] = useState(false)

  // Handle manual document refresh
  // const handleRefreshDocuments = useCallback(async () => {
  //   setIsRefreshing(true)
  //   try {
  //     await updateDocuments()
  //     toast({
  //       title: 'Documents refreshed',
  //       description: 'Document list has been updated'
  //     })
  //   } catch (error) {
  //     console.error('Error refreshing documents:', error)
  //     toast({
  //       title: 'Failed to refresh documents',
  //       description: 'An error occurred while refreshing the document list',
  //       variant: 'destructive'
  //     })
  //   } finally {
  //     setIsRefreshing(false)
  //   }
  // }, [updateDocuments])

  const allowMedicalChronologyGeneration =
    usageStats.used < usageStats.available

  return (
    <div className="space-y-4">
      {/* Document refresh section */}
      {/* <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          {isPolling && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Checking for new documents...</span>
            </div>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefreshDocuments}
          disabled={isRefreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw
            className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`}
          />
          {isRefreshing ? 'Refreshing...' : 'Refresh Documents'}
        </Button>
      </div> */}

      <Tabs
        value={currentTab}
        onValueChange={handleTabChange}
        className="mt-6 w-full"
      >
        <TabsList
          className="w-full justify-center gap-2"
          aria-label="Medical chronology generation steps"
        >
          <TabsTrigger
            value={STEPS.SELECT}
            disabled={isGenerating}
            aria-label="Step 1: Select Documents"
          >
            Step 1: Select Documents
          </TabsTrigger>
          <ArrowRightCircle className="w-5 h-5" aria-hidden="true" />
          <TabsTrigger
            value={STEPS.ADDITIONAL}
            disabled={isGenerating}
            aria-label="Step 2: Additional Documents"
          >
            Step 2: Additional Docs
          </TabsTrigger>
          <ArrowRightCircle className="w-5 h-5" aria-hidden="true" />
          <TabsTrigger
            value={STEPS.STRATEGY}
            disabled={isGenerating}
            aria-label="Step 3: Strategise"
          >
            Step 3: Strategise
          </TabsTrigger>
          <ArrowRightCircle className="w-5 h-5" aria-hidden="true" />
          <TabsTrigger
            value={STEPS.REVIEW}
            disabled={isGenerating}
            aria-label="Step 4: Review and Finalize"
          >
            Step 4: Review &amp; Finalize
          </TabsTrigger>
          <ArrowRightCircle className="w-5 h-5" aria-hidden="true" />
          <TabsTrigger
            value={STEPS.GENERATE}
            disabled={!step5Allowed || isGenerating}
            aria-label="Step 5: Generate"
          >
            Step 5: Generate{' '}
            {isGenerating && (
              <Loader2
                className="ml-2 h-4 w-4 animate-spin"
                aria-hidden="true"
              />
            )}
          </TabsTrigger>
        </TabsList>

        {/* Step 1: Document Selection */}
        <TabsContent value={STEPS.SELECT} className="mt-4 space-y-4 w-full">
          <CaseDocumentSelector
            title="Select Documents"
            docTypes={DOCUMENT_TYPES}
            allDocuments={allDocuments}
            isPolling={isPolling}
            selectedDocumentsByType={selectedDocumentsByType}
            setSelectedDocumentsByType={setSelectedDocumentsByType}
          />
          <div className="flex justify-end mt-4">
            <Button onClick={goToNextTab}>Next Step</Button>
          </div>
        </TabsContent>

        {/* Step 2: Additional Documents */}
        <TabsContent value={STEPS.ADDITIONAL} className="mt-4 space-y-4">
          <CaseDocumentSelector
            title="Select Additional Docs"
            docTypes={['Additional Documents']}
            allDocuments={availableAdditionalDocuments}
            selectedDocumentsByType={selectedDocumentsByType}
            setSelectedDocumentsByType={setSelectedDocumentsByType}
          />
          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={goToPrevTab}>
              Previous Step
            </Button>
            <Button onClick={goToNextTab}>Next Step</Button>
          </div>
        </TabsContent>

        {/* Step 3: Strategy */}
        <TabsContent value={STEPS.STRATEGY} className="mt-4 space-y-4">
          <MedicalChronologyStrategyForm
            caseId={binder.id}
            defaultValues={strategyInputs}
            setValues={handleStrategyInputsChange}
            ref={strategyFormRef}
          />
          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={goToPrevTab}>
              Previous Step
            </Button>
            <Button onClick={goToNextTab} disabled={isGenerating}>
              Next Step
            </Button>
          </div>
        </TabsContent>

        {/* Step 4: Review & Finalize */}
        <TabsContent value={STEPS.REVIEW} className="mt-4 space-y-4">
          <ReviewAndFinalizeUploads
            selectedDocumentsByType={selectedDocumentsByType}
            allDocuments={allDocuments}
            setSelectedDocumentsByType={setSelectedDocumentsByType}
          />
          <div className="flex justify-between mt-4">
            <Button variant="outline" onClick={goToPrevTab}>
              Previous Step
            </Button>
            <Button
              onClick={handleFinalizeUploads}
              disabled={
                !areDocsProvided ||
                isGenerating ||
                !allowMedicalChronologyGeneration
              }
              aria-busy={isGenerating}
            >
              {!areDocsProvided
                ? 'At least 1 Document Required'
                : isGenerating
                  ? 'Generating...'
                  : !allowMedicalChronologyGeneration
                    ? 'Out of Credits'
                    : generatedDocument
                      ? 'Regenerate Medical Chronology'
                      : 'Finalize Uploads'}
              {isGenerating && (
                <Loader2
                  className="ml-2 h-4 w-4 animate-spin"
                  aria-hidden="true"
                />
              )}
            </Button>
          </div>
        </TabsContent>

        {/* Step 5: Generation Results */}
        <TabsContent value={STEPS.GENERATE} className="mt-4 space-y-4">
          {generatedDocument && (
            <div className="flex justify-end gap-4 mt-4">
              <Button
                onClick={handleSaveChanges}
                disabled={isSaving || !hasUnsavedChanges}
                variant="outline"
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
                {isSaving ? (
                  <Loader2
                    className="ml-2 h-4 w-4 animate-spin"
                    aria-hidden="true"
                  />
                ) : (
                  <Save className="ml-2 h-4 w-4" aria-hidden="true" />
                )}
              </Button>
              <CopyButton content={editedDocument || generatedDocument} />
              <DownloadButton
                content={editedDocument || generatedDocument}
                filename={`${binder.name} - Medical Chronology`}
                documents={allDocuments}
              />
            </div>
          )}

          {generatedDocument ? (
            <TipTapEditor
              content={generatedDocument}
              setContent={handleContentChange}
              refDocuments={allDocuments}
              isEditable={true}
            />
          ) : (
            <DocumentGeneration
              document={generatedDocument}
              feature="medicalChronology"
              waitMultiplier={10}
              startingPosition={calculateEstimatedStepPosition({
                lastUpdatedAt,
                duration: 4
              })}
            />
          )}
          {generatedDocument && (
            <div className="flex justify-center gap-4 mt-4">
              <Button
                variant="outline"
                onClick={() => setCurrentTab(STEPS.REVIEW)}
                disabled={isGenerating}
              >
                Return to Review
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
