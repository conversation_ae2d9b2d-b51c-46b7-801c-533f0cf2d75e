'use client'

import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Check, Loader2 } from 'lucide-react'
import { BinderFeature, CASE_FEATURE_GENERATION_TEXT } from '@/config/dashboard'

const DocumentGeneration = ({
  document,
  feature,
  waitMultiplier = 1,
  startingPosition = 0
}: {
  document: string | null
  feature?: BinderFeature
  waitMultiplier?: number
  startingPosition?: number
}) => {
  const [scanPosition, setScanPosition] = useState(0)
  const [scanDirection, setScanDirection] = useState(1) // 1 for down, -1 for up
  const [processStep, setProcessStep] = useState(startingPosition)
  const [revealProgress, setRevealProgress] = useState(0)

  const processSteps = feature
    ? CASE_FEATURE_GENERATION_TEXT[feature]
    : Array(5).fill('Please wait...')

  // Scanning line animation effect
  useEffect(() => {
    const scanInterval = setInterval(() => {
      setScanPosition((prev) => {
        const newPosition = prev + scanDirection * 2

        if (newPosition >= 100) {
          setScanDirection(-1)
          return 100
        } else if (newPosition <= 0) {
          setScanDirection(1)
          return 0
        }

        return newPosition
      })
    }, 50)

    return () => clearInterval(scanInterval)
  }, [scanDirection])

  // Process progression effect
  useEffect(() => {
    const processInterval = setInterval(() => {
      setProcessStep((prev) => {
        if (prev < processSteps.length - 1) {
          return prev + 1
        }
        return prev
      })
    }, 10000 * waitMultiplier)

    return () => clearInterval(processInterval)
  }, [processSteps.length])

  // Control document reveal animation
  useEffect(() => {
    if (document) {
      // Start the reveal animation when document becomes available
      setRevealProgress(0)

      const revealInterval = setInterval(() => {
        setRevealProgress((prev) => {
          if (prev < 100) {
            return prev + 1
          } else {
            clearInterval(revealInterval)
            return 100
          }
        })
      }, 20) // Adjust speed as needed

      return () => clearInterval(revealInterval)
    }
  }, [document])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Document Generation</CardTitle>
        <CardDescription>
          {document
            ? 'Your Medical Chronology Report is now complete and ready for use.'
            : processSteps[processStep]}
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="relative bg-white p-4 rounded-lg border border-gray-200">
          {!document && (
            <>
              {/* Process steps with current step animated and completed steps checked */}
              <div className="space-y-3">
                {processSteps.map((step, index) => (
                  <div key={index} className="flex items-center gap-3">
                    {index < processStep ? (
                      <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                    ) : index === processStep ? (
                      <Loader2 className="h-5 w-5 text-amber-500 animate-spin flex-shrink-0" />
                    ) : (
                      <div className="h-5 w-5 flex-shrink-0" /> // Empty space for alignment
                    )}

                    {index === processStep ? (
                      <div className="text-amber-600 font-medium">{step}</div>
                    ) : index < processStep ? (
                      <div className="text-gray-500">{step}</div>
                    ) : (
                      <Skeleton className="h-6 w-full" />
                    )}
                  </div>
                ))}
              </div>

              {/* Neon scanning line */}
              <div
                className="absolute left-0 right-0 h-1 bg-amber-300 shadow-lg shadow-amber-bg-amber-300/50"
                style={{
                  top: `${scanPosition}%`,
                  boxShadow:
                    '0 0 10px 2px rgba(251, 191, 36, 0.7), 0 0 20px 4px rgba(251, 191, 36, 0.5)',
                  transition: 'top 0.05s linear'
                }}
              />
            </>
          )}

          {/* Completed document with top-to-bottom reveal */}
          {document && (
            <div className="relative overflow-hidden bg-white p-0 rounded-lg">
              {/* Gradient mask for top-to-bottom reveal */}
              <div
                className="absolute inset-0 z-10 pointer-events-none"
                style={{
                  background: `linear-gradient(to bottom, rgba(255,255,255,0) ${revealProgress}%, rgba(255,255,255,1) ${revealProgress}%)`
                }}
              />
            </div>
          )}
        </div>

        {/* Process indicator */}
        <div className="mt-4 w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-amber-400 xdark:bg-amber-300 h-2 rounded-full transition-all duration-500 ease-out"
            style={{
              width: document
                ? '100%'
                : `${(processStep / (processSteps.length - 1)) * 100}%`
            }}
          />
        </div>
      </CardContent>
    </Card>
  )
}

export default DocumentGeneration
