import { db } from '../../db'
import { logger } from '../utils-llm'
import { DocumentContent } from '@/types/case'
import { ExtractedEvent, ExistingEventsStatus } from './types'
import { findDocTypeForDocument } from './utils'
import { MAX_EVENTS_FOR_PROCESSING } from './constants'

// Utility function to check existing events status
export async function checkExistingEventsStatus(
  binderId: string,
  documentIds: number[]
): Promise<ExistingEventsStatus> {
  const existingEvents = await db.documentEvent.findMany({
    where: {
      binderId,
      documentId: { in: documentIds }
    },
    select: {
      documentId: true,
      processed: true
    }
  })

  const eventsByDoc = existingEvents.reduce(
    (acc, event) => {
      if (!acc[event.documentId]) {
        acc[event.documentId] = { total: 0, processed: 0 }
      }
      acc[event.documentId].total++
      if (event.processed) {
        acc[event.documentId].processed++
      }
      return acc
    },
    {} as Record<number, { total: number; processed: number }>
  )

  const documentsWithEvents = Object.keys(eventsByDoc).map(Number)
  const documentsWithoutEvents = documentIds.filter(
    (id) => !documentsWithEvents.includes(id)
  )

  return {
    documentsWithEvents,
    documentsWithoutEvents,
    eventsByDoc,
    totalExistingEvents: existingEvents.length,
    totalProcessedEvents: existingEvents.filter((e) => e.processed).length
  }
}

// Helper function to structure extracted data from existing events
export function structureExtractedDataFromEvents(
  existingEvents: ExtractedEvent[],
  documents: Record<string, DocumentContent[]>
): Record<string, any> {
  const extractedData: Record<string, any> = {}

  // Initialize structure for each document type
  for (const docType of Object.keys(documents)) {
    extractedData[docType] = []
  }

  // Group events by document
  const eventsByDocument = existingEvents.reduce(
    (acc, event) => {
      const docId = event.sourceDocumentId
      if (docId && !acc[docId]) {
        acc[docId] = []
      }
      if (docId) {
        acc[docId].push(event)
      }
      return acc
    },
    {} as Record<string | number, ExtractedEvent[]>
  )

  // Create structured data for each document
  Object.values(documents)
    .flat()
    .forEach((doc) => {
      const docEvents = eventsByDocument[doc.id] || []
      const docType = findDocTypeForDocument(doc.id, documents)

      if (docType && extractedData[docType]) {
        extractedData[docType].push({
          sourceDocumentId: doc.id,
          docType,
          title: doc.title,
          events: docEvents,
          rawContent: doc.content.substring(0, 500) + '...',
          extractedEventsCount: docEvents.length,
          avgRelevanceScore:
            docEvents.length > 0
              ? (
                  docEvents.reduce((sum, e) => sum + e.relevanceScore, 0) /
                  docEvents.length
                ).toFixed(2)
              : 0
        })
      }
    })

  return extractedData
}

// Helper function to filter documents that need processing
export function filterDocumentsToProcess(
  documents: Record<string, DocumentContent[]>,
  documentsWithoutEvents: number[]
): Record<string, DocumentContent[]> {
  const filtered: Record<string, DocumentContent[]> = {}

  for (const [docType, docs] of Object.entries(documents)) {
    filtered[docType] = docs.filter((doc) =>
      documentsWithoutEvents.includes(doc.id)
    )
  }

  return filtered
}

// Helper function to merge existing and new extracted data
export function mergeExtractedData(
  newExtractedData: Record<string, any>,
  existingEvents: ExtractedEvent[],
  documents: Record<string, DocumentContent[]>
): Record<string, any> {
  const mergedData: Record<string, any> = {}

  // Initialize structure for each document type
  for (const docType of Object.keys(documents)) {
    mergedData[docType] = []
  }

  // Group existing events by document
  const existingEventsByDocument = existingEvents.reduce(
    (acc, event) => {
      const docId = event.sourceDocumentId
      if (docId && !acc[docId]) {
        acc[docId] = []
      }
      if (docId) {
        acc[docId].push(event)
      }
      return acc
    },
    {} as Record<string | number, ExtractedEvent[]>
  )

  // Process all documents
  Object.values(documents)
    .flat()
    .forEach((doc) => {
      const docType = findDocTypeForDocument(doc.id, documents)
      if (!docType || !mergedData[docType]) return

      // Check if this document was newly processed
      const newDocData = newExtractedData[docType]?.find(
        (d: any) => d.sourceDocumentId === doc.id
      )

      if (newDocData) {
        // Use new data
        mergedData[docType].push(newDocData)
      } else {
        // Use existing events
        const existingDocEvents = existingEventsByDocument[doc.id] || []
        mergedData[docType].push({
          sourceDocumentId: doc.id,
          docType,
          title: doc.title,
          events: existingDocEvents,
          rawContent: doc.content.substring(0, 500) + '...',
          extractedEventsCount: existingDocEvents.length
        })
      }
    })

  return mergedData
}

// Helper function to fetch deduplicated events from database
export async function fetchDeduplicatedEvents(
  binderId: string,
  documents: Record<string, DocumentContent[]>
): Promise<ExtractedEvent[]> {
  const events = await db.documentEvent.findMany({
    where: { binderId },
    orderBy: { relevanceScore: 'desc' }, // Order by relevance score descending
    take: MAX_EVENTS_FOR_PROCESSING // Limit to top events by relevance
  })

  logger.info(
    `📊 Fetched top ${events.length} events by relevance score (max ${MAX_EVENTS_FOR_PROCESSING})`
  )

  return events.map((event) => ({
    event: event.event,
    eventType: event.eventType,
    eventDescription: event.eventDescription,
    timestamp: event.timestamp.toISOString(),
    estimatedTime: event.estimatedTime,
    confidence: 0.9,
    relevanceScore: event.relevanceScore || 0.0,
    pageReference: event.pageRange,
    sourceDocumentId: event.documentId,
    docType: findDocTypeForDocument(event.documentId, documents),
    provider: (event.rawExtractedData as any)?.provider,
    providerSpecialty: (event.rawExtractedData as any)?.providerSpecialty,
    department: (event.rawExtractedData as any)?.department,
    facility: (event.rawExtractedData as any)?.facility
  }))
}

// Helper function to clean up existing events for a binder
export async function cleanupExistingEvents(
  binderId: string,
  forceCleanup: boolean = false
) {
  if (forceCleanup) {
    // Delete all existing events for this binder
    await db.documentEvent.deleteMany({
      where: { binderId }
    })
    logger.info(`Cleaned up all existing events for binder: ${binderId}`)
  } else {
    // Only delete unprocessed events to allow for re-processing
    const deletedCount = await db.documentEvent.deleteMany({
      where: { binderId, processed: false }
    })
    logger.info(
      `Cleaned up ${deletedCount.count} unprocessed events for binder: ${binderId}`
    )
  }
}

// Helper function to get event relevance statistics
export async function getEventRelevanceStats(binderId: string) {
  const stats = await db.documentEvent.groupBy({
    by: ['relevanceScore'],
    where: { binderId, processed: true },
    _count: true,
    orderBy: { relevanceScore: 'desc' }
  })

  const totalEvents = await db.documentEvent.count({
    where: { binderId, processed: true }
  })

  const relevanceDistribution = {
    critical: stats
      .filter((s) => (s.relevanceScore || 0) >= 0.9)
      .reduce((sum, s) => sum + s._count, 0),
    important: stats
      .filter(
        (s) => (s.relevanceScore || 0) >= 0.7 && (s.relevanceScore || 0) < 0.9
      )
      .reduce((sum, s) => sum + s._count, 0),
    supporting: stats
      .filter(
        (s) => (s.relevanceScore || 0) >= 0.5 && (s.relevanceScore || 0) < 0.7
      )
      .reduce((sum, s) => sum + s._count, 0),
    minor: stats
      .filter(
        (s) => (s.relevanceScore || 0) >= 0.3 && (s.relevanceScore || 0) < 0.5
      )
      .reduce((sum, s) => sum + s._count, 0),
    background: stats
      .filter((s) => (s.relevanceScore || 0) < 0.3)
      .reduce((sum, s) => sum + s._count, 0)
  }

  logger.info('📊 Event Relevance Distribution:', {
    total: totalEvents,
    critical: `${relevanceDistribution.critical} (${((relevanceDistribution.critical / totalEvents) * 100).toFixed(1)}%)`,
    important: `${relevanceDistribution.important} (${((relevanceDistribution.important / totalEvents) * 100).toFixed(1)}%)`,
    supporting: `${relevanceDistribution.supporting} (${((relevanceDistribution.supporting / totalEvents) * 100).toFixed(1)}%)`,
    minor: `${relevanceDistribution.minor} (${((relevanceDistribution.minor / totalEvents) * 100).toFixed(1)}%)`,
    background: `${relevanceDistribution.background} (${((relevanceDistribution.background / totalEvents) * 100).toFixed(1)}%)`
  })

  return relevanceDistribution
}
