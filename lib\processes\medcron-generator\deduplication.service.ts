import pLimit from 'p-limit'
import { db } from '../../db'
import { createAzureCompletion } from '../../services/azure-openai-service'
import { logger } from '../utils-llm'
import { AuthUser } from 'next-auth'
import { MedicalChronologyStrategyFormData } from '@/components/elements/forms/medical-chronology-strategy-form'
import { GPTModel } from '@/types'
import { ErrorCollector } from './types'
import { chunkArray } from './utils'

// Deduplicate a batch of events
export async function deduplicateBatch(
  events: any[],
  errorCollector: ErrorCollector,
  strategyInputs: Partial<{
    treatmentTimelineGuidelines?: string | undefined
    prospectiveCareGuidelines?: string | undefined
  }>,
  user: AuthUser
) {
  if (events.length < 2) return

  // Fetch the deduplication prompt from database
  const deduplicationPrompt = await db.prompt.findFirst({
    where: {
      source: 'AI_MEDICAL_CHRONOLOGY_EVENT_DEDUPLICATION'
    }
  })

  if (!deduplicationPrompt) {
    throw new Error(
      'Event deduplication prompt not found in database. Please ensure AI_MEDICAL_CHRONOLOGY_EVENT_DEDUPLICATION prompt is properly configured.'
    )
  }

  // Build strategy guidelines section for variable replacement
  const strategyGuidelines = [
    strategyInputs?.treatmentTimelineGuidelines?.trim()
      ? `STRATEGY GUIDANCE - Treatment Timeline Focus:\n${strategyInputs.treatmentTimelineGuidelines}\n\nWhen merging duplicate events, prioritize information that supports the treatment timeline strategy above.`
      : '',
    strategyInputs?.prospectiveCareGuidelines?.trim()
      ? `STRATEGY GUIDANCE - Prospective Care Strategy:\n${strategyInputs.prospectiveCareGuidelines}\n\nWhen merging events related to ongoing or future care, ensure the merged descriptions highlight prospective care elements outlined above.`
      : ''
  ]
    .filter(Boolean)
    .join('\n\n')

  // Prepare events data for the prompt
  const eventsJson = JSON.stringify(
    events.map((e) => ({
      id: e.id,
      event: e.event,
      eventType: e.eventType,
      timestamp: e.timestamp,
      description: e.eventDescription,
      documentId: e.documentId,
      relevanceScore: e.relevanceScore || 0.5,
      provider: e.rawExtractedData?.provider,
      providerSpecialty: e.rawExtractedData?.providerSpecialty,
      department: e.rawExtractedData?.department,
      facility: e.rawExtractedData?.facility
    })),
    null,
    2
  )

  // Replace variables in the prompt template
  const prompt = deduplicationPrompt.prompt
    .replace('{{strategyGuidelines}}', strategyGuidelines)
    .replace('{{eventsJson}}', eventsJson)

  try {
    console.log(`🔄 Deduplicating ${events.length} events...`)

    const response = await createAzureCompletion({
      messages: [
        {
          role: 'system',
          content: prompt + '\n' + deduplicationPrompt.expectedOutput
        }
      ],
      model: GPTModel.GPTo4Mini,
      json: true,
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'event-deduplication'
    })

    const result = response as {
      duplicateGroups?: {
        keepEventId: string
        mergeEventIds: string[]
        mergedEvent: string
        mergedDescription: string
        mergedRelevanceScore?: number
        mergedProvider?: string
        mergedProviderSpecialty?: string
        mergedDepartment?: string
        mergedFacility?: string
      }[]
      uniqueEventIds?: string[]
    }

    // Process duplicate groups
    for (const group of result.duplicateGroups || []) {
      try {
        // Get the original event to preserve its rawExtractedData
        const originalEvent = await db.documentEvent.findUnique({
          where: { id: group.keepEventId }
        })

        if (originalEvent) {
          // Update rawExtractedData with merged provider info
          const rawExtractedData = (originalEvent.rawExtractedData ||
            {}) as Record<string, any>
          const updatedRawData = {
            ...rawExtractedData,
            provider: group.mergedProvider || rawExtractedData?.provider,
            providerSpecialty:
              group.mergedProviderSpecialty ||
              rawExtractedData?.providerSpecialty,
            department: group.mergedDepartment || rawExtractedData?.department,
            facility: group.mergedFacility || rawExtractedData?.facility
          }

          // Update the kept event with merged information
          await db.documentEvent.update({
            where: { id: group.keepEventId },
            data: {
              event: group.mergedEvent,
              eventDescription: group.mergedDescription,
              relevanceScore:
                group.mergedRelevanceScore || originalEvent.relevanceScore,
              rawExtractedData: updatedRawData,
              processed: true
            }
          })
        }

        // Delete duplicate events
        await db.documentEvent.deleteMany({
          where: { id: { in: group.mergeEventIds } }
        })
        errorCollector.reset()
      } catch (error) {
        errorCollector.addError(
          error instanceof Error ? error.message : 'Unknown DB error',
          `merge-group-${group.keepEventId}`
        )
      }
    }

    await db.documentEvent.updateMany({
      where: { id: { in: events.map((e) => e.id) } },
      data: { processed: true }
    })

    errorCollector.reset()
  } catch (error) {
    errorCollector.addError(
      error instanceof Error ? error.message : 'Unknown error',
      'deduplication-llm'
    )
    console.error('Error in deduplication:', error)
    // Mark all as processed to avoid infinite loops
    try {
      await db.documentEvent.updateMany({
        where: { id: { in: events.map((e) => e.id) } },
        data: { processed: true }
      })
    } catch (dbError) {
      errorCollector.addError(
        dbError instanceof Error ? dbError.message : 'Unknown DB error',
        'mark-processed-fallback'
      )
    }
  }
}

/**
 * Enhanced deduplication with batch processing using optimized architecture.
 *
 * This implementation uses a 3-level batching strategy:
 * Level 1: Database pagination (200 events at a time)
 * Level 2: Processing sub-batches (30 events each)
 * Level 3: Parallel processing with concurrency control (up to 20 concurrent batches)
 *
 * Benefits:
 * - Constant memory usage regardless of dataset size
 * - Fault isolation (one batch failure doesn't stop entire process)
 * - Optimal throughput with parallel processing
 * - Progress tracking for long-running operations
 * - Database efficiency with smaller queries
 */
export async function deduplicateEventsWithBatching(
  binderId: string,
  errorCollector: ErrorCollector,
  strategyInputs: MedicalChronologyStrategyFormData,
  user: AuthUser
) {
  const BATCH_SIZE = 200 // Fetch 200 events from DB at a time for optimal performance
  const PROCESSING_BATCH_SIZE = 30 // Process in smaller batches of 30 events
  const MAX_CONCURRENT_BATCHES = 20 // Conservative limit for deduplication stability

  let skip = 0
  const limit = pLimit(MAX_CONCURRENT_BATCHES)

  logger.info('🔄 Starting batch-based deduplication process')

  while (true) {
    // Level 1: Database pagination - fetch events in batches of 200
    const events = await db.documentEvent.findMany({
      where: { binderId, processed: false },
      orderBy: { timestamp: 'asc' },
      skip,
      take: BATCH_SIZE
    })

    if (events.length === 0) {
      logger.info('✅ No more unprocessed events to deduplicate')
      break
    }

    logger.info(
      `📊 Processing batch ${Math.floor(skip / BATCH_SIZE) + 1}: ${events.length} events (skip: ${skip})`
    )

    // Level 2: Split into smaller processing batches
    const processingBatches = chunkArray(events, PROCESSING_BATCH_SIZE)
    logger.info(
      `🔄 Split into ${processingBatches.length} processing batches of ${PROCESSING_BATCH_SIZE} events each`
    )

    // Level 3: Process batches in parallel with concurrency control
    const batchPromises = processingBatches.map((batch, batchIndex) =>
      limit(async () => {
        try {
          logger.info(
            `🔄 Deduplicating batch ${batchIndex + 1}/${processingBatches.length} (${batch.length} events)`
          )

          // Use the local deduplicateBatch function
          await deduplicateBatch(batch, errorCollector, strategyInputs, user)

          logger.info(
            `✅ Completed batch ${batchIndex + 1}/${processingBatches.length}`
          )
          errorCollector.reset() // Reset consecutive counter on success
        } catch (error) {
          errorCollector.addError(
            error instanceof Error ? error.message : 'Unknown error',
            `deduplicate-batch-${batch.length}`
          )
          if (errorCollector.shouldExitProcess()) {
            throw new Error(`Too many consecutive errors in deduplication`)
          }
        }
      })
    )

    // Wait for all batches in this round to complete
    await Promise.all(batchPromises)

    logger.info(
      `✅ Completed processing batch ${Math.floor(skip / BATCH_SIZE) + 1}`
    )

    // Memory management - force garbage collection if available
    if (global.gc) {
      global.gc()
    }

    skip += BATCH_SIZE
  }

  logger.info('✅ Batch deduplication process completed')
}
