'use client'

import { usePathname, useRouter } from 'next/navigation'
import { Button } from '../../ui/button'
import { Input } from '../../ui/input'
import { useState } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../../ui/select'

export interface FilterSearchInputProps {
  param: string
  placeholder: string
  options?: { value: string; label: string }[]
  value?: string
}

export function FilterSearchInput({
  textinput,
  selectinput
}: {
  textinput: FilterSearchInputProps[]
  selectinput?: FilterSearchInputProps[]
}) {
  const router = useRouter()
  const pathname = usePathname()

  const [inputValues, setInputValues] = useState<{ [key: string]: string }>(
    () => {
      const textInputs = textinput.reduce(
        (acc, curr) => ({
          ...acc,
          [curr.param]: curr.value || ''
        }),
        {}
      )
      const selectInputs =
        selectinput?.reduce(
          (acc, curr) => ({
            ...acc,
            [curr.param]: curr.value || ''
          }),
          {}
        ) || {}
      return { ...textInputs, ...selectInputs }
    }
  )

  const handleChange = (param: string, value: string) => {
    setInputValues((prev) => ({ ...prev, [param]: value }))
  }

  function handleSearch() {
    const queryParams = Object.keys(inputValues)
      .filter((key) => inputValues[key] && inputValues[key].length > 0)
      .map(
        (key) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(inputValues[key])}`
      )
      .join('&')
    router.push(`${pathname}?${queryParams}`)
  }

  return (
    <div className="flex w-full max-w-md items-center space-x-2">
      {textinput.map((f, index) => (
        <Input
          key={index}
          type="text"
          placeholder={f.placeholder}
          defaultValue={f.value}
          onChange={(e) => handleChange(f.param, e.target.value)}
        />
      ))}

      {selectinput?.map((s, index) => (
        <Select
          key={`select-${index}`}
          value={inputValues[s.param] || undefined}
          onValueChange={(value) => handleChange(s.param, value)}
        >
          <SelectTrigger className="bg-background xdark:bg-slate-950">
            <SelectValue placeholder={s.param} />
          </SelectTrigger>
          <SelectContent>
            {s.options?.map((option, idx) => (
              <SelectItem key={idx} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      ))}

      <Button onClick={handleSearch}>Search</Button>
    </div>
  )
}
