import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { QuestionIntent, RESEARCH_QUERY_TYPE } from '@/types'
import { Badge } from '../../ui/badge'

export function ChatQueryClarificationModal({
  open,
  onOpenChange,
  questions,
  setInput,
  setEventQuery
}: {
  open: boolean
  onOpenChange: (open: boolean) => void
  questions: QuestionIntent[]
  setInput: (input: string) => void
  setEventQuery: (eventQuery: RESEARCH_QUERY_TYPE | null) => void
}) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Let&apos;s get some clarity</DialogTitle>
          <DialogDescription>
            To better assist you, select a question that best describes your
            query.
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col-reverse space-y-4">
          {questions.map((query, index) => (
            <div key={index}>
              {index === 0 && (
                <div className="relative flex items-center w-full my-6">
                  <hr className="w-full border-gray-300 border-dashed" />
                  <span className="absolute px-2 bg-background xdark:bg-slate-950 text-sm left-1/2 transform -translate-x-1/2">
                    or continue with your original question
                  </span>
                </div>
              )}
              <Button
                variant={index === 0 ? 'default' : 'secondary'}
                onClick={() => {
                  setInput(query.question)
                  setEventQuery(RESEARCH_QUERY_TYPE.ASK)
                  onOpenChange(false)
                }}
                className="text-left justify-start h-auto w-full hover:bg-accent xdark:hover:bg-ring xdark:hover:text-slate-900 flex flex-col items-start"
              >
                <div>{query.question}</div>
                {index !== 0 && (
                  <Badge variant="default" className="mt-2">
                    {query.intent}
                  </Badge>
                )}
              </Button>
            </div>
          ))}
        </div>
        <DialogFooter className="sm:justify-end">
          <DialogClose asChild>
            <Button type="button" variant="secondary">
              Close
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
