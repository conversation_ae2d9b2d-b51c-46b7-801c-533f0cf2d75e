'use client'

import { useState, useEffect } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { PromptList } from './prompt-list'
import { Prompt } from '@prisma/client'

// Feature groups based on the prompt names provided
const FEATURE_GROUPS = {
  AI_MEDICAL_CHRONOLOGY: {
    label: 'Medical Chronology',
    prompts: [
      'AI_MEDICAL_CHRONOLOGY_CASE_INFORMATION_METADATA',
      'AI_MEDICAL_CHRONOLOGY_DIAGNOSTIC_HIGHLIGHTS',
      'AI_MEDICAL_CHRONOLOGY_TREATMENT_CALENDAR',
      'AI_MEDICAL_CHRONOLOGY_FLAGS_AND_CASE_GAPS',
      'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE',
      'AI_MEDICAL_CHRONOLOGY_CASE_STRENGTH_ANALYSIS',
      'AI_MEDICAL_CHRONOLOGY_DOCUMENT_EXTRACTION',
      'AI_MEDICAL_CHRONOLOGY_STRUCTURE_METADATA',
      'AI_MEDICAL_CHRONOLOGY_ORGANIZE_EVENTS',
      'AI_MEDICAL_CHRONOLOGY_CASE_GAPS',
      'AI_MEDICAL_CHRONOLOGY_MULTIPLE_INJURY_PIPELINE',
      'AI_MEDICAL_CHRONOLOGY_PROCESS_STRATEGY',
      'AI_MEDICAL_CHRONOLOGY_EVENT_EXTRACTION',
      'AI_MEDICAL_CHRONOLOGY_EVENT_DEDUPLICATION'
    ]
  },
  AI_CASE_EVALUATION: {
    label: 'Case Evaluation',
    prompts: [
      'AI_CASE_EVALUATION_PROCESS_ATTORNEY_INSIGHTS',
      'AI_CASE_EVALUATION_ASSESS_LIABILITY',
      'AI_CASE_EVALUATION_CALCULATE_ECONOMIC_DAMAGES',
      'AI_CASE_EVALUATION_EVALUATE_NONECONOMIC_DAMAGES',
      'AI_CASE_EVALUATION_ASSESS_PUNITIVE_DAMAGES',
      'AI_CASE_EVALUATION_IDENTIFY_CASE_RISKS',
      'AI_CASE_EVALUATION_DEVELOP_LITIGATION_STRATEGY',
      'AI_CASE_EVALUATION_CASE_OVERVIEW',
      'AI_CASE_EVALUATION_LIABILITY_ASSESSMENT',
      'AI_CASE_EVALUATION_DAMAGES_CALCULATION',
      'AI_CASE_EVALUATION_RISK_ANALYSIS',
      'AI_CASE_EVALUATION_LITIGATION_STRATEGY',
      'AI_CASE_EVALUATION_ATTORNEY_INSIGHTS',
      'AI_CASE_EVALUATION_FINAL_RECOMMENDATION',
      'AI_CASE_EVALUATION_ANALYZE_DEFENSE_PERSPECTIVE',
      'AI_CASE_EVALUATION_GENERATE_REVISED_REPORT'
    ]
  },
  AI_DEMAND_LETTER: {
    label: 'Demand Letter',
    prompts: [
      'AI_DEMAND_LETTER_COVER_LETTER',
      'AI_DEMAND_LETTER_FACTS_AND_LIABILITY',
      'AI_DEMAND_LETTER_INJURIES_AND_TREATMENTS',
      'AI_DEMAND_LETTER_DAMAGES_OVERVIEW',
      'AI_DEMAND_LETTER_PAST_MEDICAL_EXPENSES',
      'AI_DEMAND_LETTER_FUTURE_MEDICAL_EXPENSES',
      'AI_DEMAND_LETTER_LOSS_OF_INCOME',
      'AI_DEMAND_LETTER_LOSS_OF_HOUSEHOLD_SERVICES',
      'AI_DEMAND_LETTER_PAIN_AND_SUFFERING',
      'AI_DEMAND_LETTER_PUNITIVE_DAMAGES',
      'AI_DEMAND_LETTER_SETTLEMENT_DEMAND',
      'AI_DEMAND_LETTER_EXHIBIT_LIST'
    ]
  }
} as const

type FeatureKey = keyof typeof FEATURE_GROUPS

async function fetchPromptsByFeature(feature: FeatureKey): Promise<Prompt[]> {
  const response = await fetch(`/api/prompts/by-feature?feature=${feature}`)
  if (!response.ok) {
    throw new Error('Failed to fetch prompts')
  }
  return response.json()
}

export function PromptManagerWithFeatures() {
  const [selectedFeature, setSelectedFeature] = useState<FeatureKey | ''>(
    'AI_MEDICAL_CHRONOLOGY'
  )
  const [prompts, setPrompts] = useState<Prompt[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (selectedFeature) {
      setLoading(true)
      fetchPromptsByFeature(selectedFeature)
        .then(setPrompts)
        .catch(console.error)
        .finally(() => setLoading(false))
    } else {
      setPrompts([])
    }
  }, [selectedFeature])

  return (
    <div className="space-y-4">
      <div className="w-full max-w-sm">
        <Select
          value={selectedFeature}
          onValueChange={(value) => setSelectedFeature(value as FeatureKey)}
          defaultValue="AI_MEDICAL_CHRONOLOGY"
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a feature to view prompts" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(FEATURE_GROUPS).map(([key, group]) => (
              <SelectItem key={key} value={key}>
                {group.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {loading && (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      )}

      {selectedFeature && !loading && (
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">
            {FEATURE_GROUPS[selectedFeature].label} Prompts ({prompts.length})
          </h3>
          <PromptList prompts={prompts} />
        </div>
      )}

      {selectedFeature && !loading && prompts.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          No prompts found for {FEATURE_GROUPS[selectedFeature].label}
        </div>
      )}
    </div>
  )
}
