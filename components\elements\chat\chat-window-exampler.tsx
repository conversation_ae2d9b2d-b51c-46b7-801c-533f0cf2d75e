'use client'

import { useRef, useState, ReactElement, useEffect } from 'react'
import { ChatMessageBubble } from './chat-message-bubble'
import { Input } from '../../ui/input'
import { cn } from '@/lib/utils'
import { buttonVariants } from '../../ui/button'
import type { Message } from 'ai/react'
import Link from 'next/link'

export function ChatWindowExampler(props: {
  emptyStateComponent?: ReactElement
  sampleQuestions: {
    question: string
    answer: string
  }[]
}) {
  const messageContainerRef = useRef<HTMLDivElement | null>(null)

  const [messages, setMessages] = useState<Message[]>([])
  async function handleClick(index: number) {
    // find the element by id and add class hidden
    document
      .getElementById(`sample-questions-${index}`)
      ?.classList.add('hidden')

    // Add user's question to the messages
    setMessages((prev) => [
      ...prev,
      {
        id: String(prev.length),
        content: props.sampleQuestions[index].question,
        role: 'user'
      }
    ])

    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Simulate AI response streaming
    const answerWords = props.sampleQuestions[index].answer.split(' ')
    let currentContent = ''

    for (const word of answerWords) {
      currentContent += word + ' '
      // Introducing a delay to simulate typing
      await new Promise((resolve) => setTimeout(resolve, 100))
      setMessages((prev) => {
        const newMessages = [...prev]
        if (newMessages[newMessages.length - 1]?.role === 'system') {
          // Update last system message
          newMessages[newMessages.length - 1].content = currentContent
        } else {
          // Or add a new system message if it doesn't exist
          newMessages.push({
            id: String(prev.length),
            content: currentContent,
            role: 'system'
          })
        }
        return newMessages
      })
    }
  }

  return (
    <div
      className={`flex flex-col items-center p-4 md:p-8 rounded grow overflow-hidden border`}
    >
      {messages.length === 0 && props.emptyStateComponent
        ? props.emptyStateComponent
        : ''}

      {messages.length > 0 && (
        <div
          className="flex flex-col-reverse w-full mb-4 overflow-auto transition-[flex-grow] ease-in-out min-h-[350px]"
          ref={messageContainerRef}
        >
          {[...messages].reverse().map((m, i) => {
            return <ChatMessageBubble key={m.id} message={m} sources={[]} />
          })}
        </div>
      )}

      <div className="grid gap-2 w-full">
        {props.sampleQuestions?.map((topic, index) => (
          <button
            id={`sample-questions-${index}`}
            key={index}
            className="rounded-2xl bg-muted hover:bg-amber-600 px-4 py-1.5 text-sm font-medium w-fit"
            onClick={() => handleClick(index)}
          >
            <span>{topic.question}</span>
          </button>
        ))}
      </div>

      <form className="flex w-full flex-col">
        <div className="flex w-full mt-4">
          <Input
            className="grow mr-4 p-4 rounded"
            placeholder={'What is changing in BNSS?'}
          />

          <Link
            href={'/register'}
            className={cn([buttonVariants({}), 'min-w-fit'])}
          >
            <span>Sign Up</span>
          </Link>
        </div>
      </form>
    </div>
  )
}
