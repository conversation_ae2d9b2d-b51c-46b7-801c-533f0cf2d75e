import { ContextData, DocumentTitle, PromptStore } from '@/types/case'
import { CaseFileType } from '@prisma/client'
import pLimit from 'p-limit'
import { GoogleGenAI } from '@google/genai'
import { env } from '@/env.mjs'

const ai = new GoogleGenAI({ apiKey: env.GEMINI_API_KEY })

// Add logging utility to track progress
export const logger = {
  start: (functionName: string, ...args: any[]) => {
    console.time(`⏱️  ${functionName}`)
    console.log(`🚀 Starting ${functionName}`, ...(args.length ? args : []))
  },
  end: (functionName: string, ...args: any[]) => {
    console.timeEnd(`⏱️  ${functionName}`)
    console.log(`✅ Completed ${functionName}`, ...(args.length ? args : []))
  },
  error: (functionName: string, error: any) => {
    console.error(`❌ Error in ${functionName}:`, error)
  },
  info: (message: string, ...args: any[]) => {
    console.log(`ℹ️ ${message}`, ...(args.length ? args : []))
  }
}

/**
 * Generate all sections in parallel batches to avoid rate limits
 * @param contextData The context data to use for generation
 * @returns Object mapping section IDs to their generated content
 */
export async function generateSectionsInBatches({
  contextData,
  prompts,
  maxContinuations,
  sectionsNeedingExtension,
  documentList
}: {
  contextData: ContextData
  prompts: PromptStore[]
  maxContinuations: number
  sectionsNeedingExtension: string[]
  documentList: DocumentTitle[]
}): Promise<Record<string, string>> {
  logger.start('generateSectionsInBatches')

  try {
    // Maximum number of concurrent requests
    const MAX_CONCURRENT_REQUESTS = 5

    // Object to store generated section content
    const sections: Record<string, string> = {}

    // Create batches of sections to process
    const allSections = [...prompts]
    const batches: PromptStore[][] = []

    // Split sections into batches of MAX_CONCURRENT_REQUESTS
    for (let i = 0; i < allSections.length; i += MAX_CONCURRENT_REQUESTS) {
      batches.push(allSections.slice(i, i + MAX_CONCURRENT_REQUESTS))
    }

    logger.info(
      `Created ${batches.length} batches for ${allSections.length} sections`
    )

    // Process each batch sequentially
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex]
      logger.info(
        `Processing batch ${batchIndex + 1}/${batches.length} with ${batch.length} sections`
      )

      // Create a limit for concurrent requests within the batch
      const limit = pLimit(MAX_CONCURRENT_REQUESTS)

      // Process each section in the batch concurrently
      const batchPromises = batch.map((section) =>
        limit(async () => {
          try {
            sections[section.id] = await generateSection({
              section,
              contextData,
              maxContinuations,
              sectionsNeedingExtension,
              documentList
            })
            logger.info(`Completed section: ${section.id} (${section.title})`)
          } catch (error: any) {
            logger.error(`Error generating section ${section.id}:`, error)
            // Use a placeholder for failed sections
            sections[section.id] =
              `[Error generating ${section.title}: ${error.message}]`
          }
        })
      )

      // Wait for all sections in this batch to complete
      await Promise.all(batchPromises)
      logger.info(`Completed batch ${batchIndex + 1}/${batches.length}`)
    }

    logger.end('generateSectionsInBatches', {
      generatedSections: Object.keys(sections).length,
      totalContentLength: Object.values(sections).reduce(
        (sum, content) => sum + content.length,
        0
      )
    })

    return sections
  } catch (error: any) {
    logger.error('generateSectionsInBatches', error)
    throw new Error(`Failed to generate sections in batches: ${error.message}`)
  }
}

/**
 * Generate a single section of the demand letter
 * @param section The section definition from prompts
 * @param contextData The context data to use for generation
 * @returns The generated section content
 */
export async function generateSection({
  section,
  contextData,
  maxContinuations,
  sectionsNeedingExtension,
  documentList
}: {
  section: PromptStore
  contextData: ContextData
  maxContinuations: number
  sectionsNeedingExtension: string[]
  documentList: DocumentTitle[]
}): Promise<string> {
  logger.start('generateSection', {
    sectionId: section.id,
    title: section.title
  })

  try {
    // 1. Extract only the context needed for this section
    const sectionContext: Record<string, any> = {}
    for (const contextType of section.contextNeeded) {
      if (contextData[contextType as CaseFileType]) {
        sectionContext[contextType] = contextData[contextType as CaseFileType]
      }
    }

    // 2. Create the initial prompt
    const initialPrompt = createSectionPrompt({
      section,
      context: sectionContext,
      documentList
    })

    // 3. Generate the initial content

    const response = await ai.models.generateContent({
      model: 'gemini-2.0-flash',
      contents: initialPrompt
    })

    let content = response.text || ''

    // 4. Check if the section needs extension (for complex sections)
    if (sectionNeedsExtension(section.id, content, sectionsNeedingExtension)) {
      logger.info(
        `Section ${section.id} needs extension, current length: ${content.length} characters`
      )
      // Start extension with continuation count of 0
      content = await extendSectionContent({
        section,
        context: sectionContext,
        currentContent: content,
        continuationCount: 0,
        maxContinuations,
        sectionsNeedingExtension
      })
    }

    logger.end('generateSection', {
      sectionId: section.id,
      contentLength: content.length
    })

    return content
  } catch (error: any) {
    logger.error(`generateSection:${section.id}`, error)
    throw new Error(
      `Failed to generate section ${section.id}: ${error.message}`
    )
  }
}

/**
 * Create a prompt for generating a section with appropriate context
 * @param section The section definition
 * @param context The context data for this section
 * @returns The complete prompt for the section
 */
export function createSectionPrompt({
  section,
  context,
  documentList
}: {
  section: PromptStore
  context: Record<string, any>
  documentList: DocumentTitle[]
}): string {
  // Convert context to JSON string with proper formatting
  const contextJson = JSON.stringify(context, null, 2)

  // Create the complete prompt with context and instructions
  return `
  CONTEXT INFORMATION:
  ${contextJson}
  
  SECTION: ${section.title}
  
  INSTRUCTIONS:
  ${section.prompt}

  CONTEXT DOCUMENTS:
  ${documentList
    .map((doc) => `- docID: ${doc.id} - Title: ${doc.title}`)
    .join('\n')}
  
  IMPORTANT:
  - Follow the instructions exactly, providing all requested details in the format specified
  - Use only the information provided in the context, making reasonable assumptions where necessary
  - If the content is too lengthy to complete in one response, focus on creating a comprehensive beginning
    and indicate where you need to continue
  - Do not use placeholders or incomplete information
  - You can assume page number for citation as 1, unless otherwise specified
  `
}

/**
 * Check if a section needs extension (for complex sections that might exceed model limits)
 * @param sectionId The ID of the section
 * @param content The current content of the section
 * @returns True if the section needs extension, false otherwise
 */
export function sectionNeedsExtension(
  sectionId: string,
  content: string,
  sectionsNeedingExtension: string[]
): boolean {
  // Only check complex sections
  if (!sectionsNeedingExtension.includes(sectionId)) {
    return false
  }

  // Check for signs of truncation
  const truncationSigns = [
    content.endsWith('...'), // Explicit continuation marker
    content.includes('[continued]'), // Continuation marker
    content.includes('To be continued'), // Continuation phrase
    content.match(/\d+\.\s*$/) !== null, // Ends with a numbered point
    content.endsWith(':'), // Ends with a colon
    content.length > 6000, // Very long content approaching token limits
    content.slice(-100).includes('In the next part'), // Reference to continuation
    content.slice(-100).includes('will continue') // Promise of continuation
  ]

  // For other complex sections, check for general truncation signs
  return truncationSigns.some(Boolean)
}

/**
 * Extend the content of a section that was too complex for a single generation
 * @param section The section definition
 * @param context The context data for this section
 * @param currentContent The current content of the section
 * @param continuationCount Number of continuations already performed
 * @returns The extended section content
 */
export async function extendSectionContent({
  section,
  context,
  currentContent,
  continuationCount,
  maxContinuations,
  sectionsNeedingExtension
}: {
  section: PromptStore
  context: Record<string, any>
  currentContent: string
  continuationCount: number
  maxContinuations: number
  sectionsNeedingExtension: string[]
}): Promise<string> {
  logger.start('extendSectionContent', {
    sectionId: section.id,
    continuationCount: continuationCount + 1
  })

  try {
    // Check if we've already reached the maximum number of continuations
    if (continuationCount >= maxContinuations) {
      logger.info(
        `Maximum continuations (${maxContinuations}) reached for section ${section.id}. Returning current content.`
      )
      return currentContent
    }

    // Find clean break point
    let breakPoint = currentContent.length
    const lastDoubleNewline = currentContent.lastIndexOf('\n\n')
    if (lastDoubleNewline > currentContent.length * 0.6) {
      breakPoint = lastDoubleNewline
    }

    // Extract the part that's already complete
    const completedContent = currentContent.substring(0, breakPoint).trim()

    // Create a safe continuation prompt
    const continuationPrompt = `
  You are continuing the "${section.title}" section of a legal document.
  
  - Continue naturally without repeating anything from the previous content.
  - Maintain formal, professional tone.
  - Cover any missing important points.
  - Assume the reader has read the previous part already.
  - This is continuation ${continuationCount + 1} of maximum ${maxContinuations}, so try to complete the section.
  
  Guidelines for what to generate:
  ---
  ${section.prompt}
  ---
  
  PREVIOUS CONTENT (DO NOT REPEAT):
  ---
  ${completedContent}
  ---
  
  Now continue the section from exactly where it left off from the last word.
  
  `.trim()

    // Generate the continuation
    const response = await ai.models.generateContent({
      model: 'gemini-2.0-flash',
      contents: continuationPrompt
    })

    const continuation = response.text || ''

    const cleanedContinuation = removeDuplicateBeginning(
      completedContent,
      continuation.trim()
    )

    const combinedContent =
      completedContent +
      (completedContent.endsWith('\n\n') ? '' : '\n\n') +
      cleanedContinuation

    // Check if it still needs extension (for very complex sections)
    if (
      sectionNeedsExtension(
        section.id,
        combinedContent,
        sectionsNeedingExtension
      ) &&
      continuationCount < maxContinuations - 1
    ) {
      logger.info(
        `Section ${section.id} needs another extension, current length: ${combinedContent.length} characters. Continuation count: ${continuationCount + 1}/${maxContinuations}`
      )
      // Recursive call to extend further, incrementing the continuation count
      return await extendSectionContent({
        section,
        context,
        currentContent: combinedContent,
        continuationCount: continuationCount + 1,
        maxContinuations,
        sectionsNeedingExtension
      })
    }

    logger.end('extendSectionContent', {
      sectionId: section.id,
      originalLength: currentContent.length,
      newLength: combinedContent.length,
      continuationCount: continuationCount + 1
    })

    return combinedContent
  } catch (error: any) {
    logger.error(`extendSectionContent:${section.id}`, error)
    // If extension fails, return the original content
    return currentContent
  }
}

/**
 * Efficiently removes duplicate overlap between previous content and continuation
 * by finding the largest meaningful overlap at the boundaries.
 *
 * @param {string} previous - The content that has already been processed
 * @param {string} continuation - The new content that may have duplicate beginning
 * @return {string} The continuation with any duplicate beginning removed
 */
export function removeDuplicateBeginning(
  previous: string,
  continuation: string
): string {
  if (!previous || !continuation) return continuation

  // Get meaningful lines from previous (with at least 2 words)
  const prevLines = previous.split('\n')
  const meaningfulPrevLines = []

  for (let i = prevLines.length - 1; i >= 0; i--) {
    const line = prevLines[i].trim()
    const words = line.split(/\s+/).filter((word) => word.length > 0)

    if (words.length >= 2) {
      meaningfulPrevLines.unshift(line) // Add to front to maintain order
      // Stop after collecting a reasonable number of lines for efficiency
      if (meaningfulPrevLines.length >= 5) break
    }
  }

  if (meaningfulPrevLines.length === 0) return continuation

  // Get the first paragraph from continuation for efficient comparison
  const firstParaEndIndex = continuation.indexOf('\n\n')
  const firstPara =
    firstParaEndIndex !== -1
      ? continuation.substring(0, firstParaEndIndex)
      : continuation

  // Start from the last meaningful line and check for overlap
  for (let i = meaningfulPrevLines.length - 1; i >= 0; i--) {
    const line = meaningfulPrevLines[i]
    const indexInFirstPara = firstPara.indexOf(line)

    if (indexInFirstPara !== -1) {
      // Found a match! Remove everything up to the end of this line
      const endOfLine = indexInFirstPara + line.length
      return continuation.substring(endOfLine)
    }
  }

  return continuation
}

// Utility function to retry API calls with exponential backoff
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  initialDelay = 1000
): Promise<T> {
  let retries = 0

  while (true) {
    try {
      return await fn()
    } catch (error: any) {
      retries++

      if (retries > maxRetries) {
        throw error
      }

      const delay = initialDelay * Math.pow(2, retries - 1)
      logger.info(`Retry ${retries}/${maxRetries} after ${delay}ms delay`)
      await new Promise((resolve) => setTimeout(resolve, delay))
    }
  }
}
