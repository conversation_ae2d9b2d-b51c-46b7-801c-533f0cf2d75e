import { notFound } from 'next/navigation'
import { db } from '@/lib/db'
import { ResearchType } from '@prisma/client'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ChatCreateButton } from '@/components/elements/chat/chat-create-button'
import { getCurrentUser } from '@/lib/session'
import { BinderRecentResearchList } from '@/components/elements/binder/binder-recent-research-list'
import { CaseBinderData } from '@/types/case'
import { caseBinderFeatures } from '@/config/dashboard'
import Link from 'next/link'
import { Icons } from '@/components/elements/icons'
import { Badge } from '@/components/ui/badge'

interface BinderDetailPageProps {
  params: {
    binderId: string
  }
}

export default async function BinderDetailPage({
  params
}: BinderDetailPageProps) {
  const user = await getCurrentUser()

  const binder = await db.binder.findUnique({
    where: {
      id: params.binderId
    },
    select: {
      id: true,
      name: true,
      researches: {
        select: {
          id: true,
          type: true,
          createdAt: true,
          question: true
        }
      },
      data: true
    }
  })

  if (!binder) {
    return notFound()
  }

  const caseBinderData = binder.data
    ? (binder.data as unknown as CaseBinderData)
    : null

  const caseLaws = binder.researches.filter(
    (research) => research.type === ResearchType.case
  )

  const laws = binder.researches.filter(
    (research) => research.type === ResearchType.law
  )

  const internal = binder.researches.filter(
    (research) => research.type === ResearchType.private
  )

  const documents = await db.documentRecords.findMany({
    select: {
      id: true,
      title: true
    },
    where: {
      source: user!.teamId,
      region: user!.region
    },
    orderBy: {
      title: 'asc'
    }
  })

  const cardItems = Object.values(caseBinderFeatures)

  return (
    <DashboardShell>
      <DashboardHeader
        heading={binder.name}
        text={`Attorney: ${caseBinderData?.attorney} | Case Number: ${caseBinderData?.caseNumber || '-'}`}
      />

      <div className="flex gap-2">
        <ChatCreateButton
          buttonText="New Case Research"
          user={user!}
          researchType={ResearchType.case}
          binderId={params.binderId}
        />

        <ChatCreateButton
          buttonText="New Law Research"
          user={user!}
          researchType={ResearchType.law}
          binderId={params.binderId}
        />

        <ChatCreateButton
          buttonText="New Internal Research"
          user={user!}
          researchType={ResearchType.private}
          binderId={params.binderId}
          documents={documents}
        />
      </div>

      <Card className="lg:max-w-4xl">
        <CardHeader>
          <CardTitle>What would you like to do?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid text-center lg:w-full lg:grid-cols-2 lg:text-left">
            {cardItems.map((item, index) =>
              item.link ? (
                <Link
                  key={index}
                  href={item.link.replace('{{binderId}}', params.binderId)}
                  className="flex flex-col items-center lg:items-start group rounded-lg border-4 border-transparent px-5 py-4 transition-colors hover:border-black hover:bg-ring/80 hover:xdark:border-neutral-700 hover:xdark:bg-neutral-800/30 cursor-pointer"
                >
                  <h2 className={`mb-3 text-2xl font-semibold`}>
                    {item.title}
                    <Icons.chevronRight className="mb-1 ml-1 inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none" />
                  </h2>
                  <p className={`m-0 max-w-[30ch] text-sm`}>
                    {item.description}
                  </p>
                </Link>
              ) : (
                <div
                  key={index}
                  className="flex flex-col items-center lg:items-start group rounded-lg border border-transparent px-5 py-4 transition-colors opacity-50"
                >
                  <h2 className="text-2xl font-semibold">{item.title}</h2>
                  <Badge variant="outline" className="my-2">
                    Coming Soon!
                  </Badge>
                  <p className="m-0 max-w-[30ch] text-sm opacity-50">
                    {item.description}
                  </p>
                </div>
              )
            )}
          </div>
        </CardContent>
      </Card>

      {caseLaws.length > 0 && (
        <Card className="p-4">
          <h2 className="text-lg font-semibold mb-2">Case Research</h2>
          <BinderRecentResearchList
            researchHistory={caseLaws}
            path={'/dashboard/research'}
            researchType={ResearchType.case}
          />
        </Card>
      )}

      {laws.length > 0 && (
        <Card className="p-4">
          <h2 className="text-lg font-semibold mb-2">Law Research </h2>
          <BinderRecentResearchList
            researchHistory={laws}
            path={'/dashboard/research-case'}
            researchType={ResearchType.law}
          />
        </Card>
      )}

      {internal.length > 0 && (
        <Card className="p-4">
          <h2 className="text-lg font-semibold mb-2">Internal Research</h2>
          <BinderRecentResearchList
            researchHistory={internal}
            path={'/dashboard/research-private'}
            researchType={ResearchType.private}
          />
        </Card>
      )}
    </DashboardShell>
  )
}
