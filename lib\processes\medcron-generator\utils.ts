import { EventCategory, ChronologyEvent, DocumentContent } from '@/types/case'
import { logger } from '../utils-llm'
import {
  ErrorCollector,
  ExtractedEvent,
  DocumentEventDb,
  TimelineEvent,
  ProviderInfo
} from './types'
import { MAX_CONSECUTIVE_ERRORS } from './constants'

// Error collector utility
export function createErrorCollector(): ErrorCollector {
  return {
    errors: [],
    consecutiveErrors: 0,
    hasError: false,
    addError(error: string, context?: string) {
      const errorMessage = context ? `[${context}] ${error}` : error
      this.errors.push(errorMessage)
      this.consecutiveErrors++
      this.hasError = true
      logger.error(
        'ErrorCollector',
        `Error #${this.consecutiveErrors}: ${errorMessage}`
      )
    },
    reset() {
      this.consecutiveErrors = 0
    },
    shouldExitProcess() {
      return this.consecutiveErrors >= MAX_CONSECUTIVE_ERRORS
    },
    getErrorSummary() {
      return {
        totalErrors: this.errors.length,
        consecutiveErrors: this.consecutiveErrors,
        recentErrors: this.errors.slice(-10),
        shouldExit: this.shouldExitProcess()
      }
    }
  }
}

// Utility function to split documents into chunks
export function splitDocumentIntoChunks(content: string): string[] {
  const CHUNK_SIZE = 2000
  const chunks: string[] = []
  for (let i = 0; i < content.length; i += CHUNK_SIZE) {
    chunks.push(content.substring(i, i + CHUNK_SIZE))
  }
  return chunks
}

// Utility function to chunk arrays
export function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

// Helper function to map event types to categories
export function mapEventTypeToCategory(eventType: string): EventCategory {
  const mapping: Record<string, EventCategory> = {
    police_report: EventCategory.PoliceReport,
    emergency_visit: EventCategory.EDAdmission,
    diagnostic_test: EventCategory.ImagingDiagnostics,
    medical_treatment: EventCategory.Treatment,
    injury_report: EventCategory.PoliceReport,
    medication: EventCategory.Treatment,
    surgery: EventCategory.Treatment,
    therapy: EventCategory.Treatment,
    expert_opinion: EventCategory.Treatment,
    legal_consultation: EventCategory.Treatment,
    disability_assessment: EventCategory.Treatment,
    work_restriction: EventCategory.Treatment,
    pain_documentation: EventCategory.Treatment
  }
  return mapping[eventType] || EventCategory.Treatment
}

// Adapter functions to convert ChronologyEvent to formats expected by imported utilities
export function convertChronologyEventsToDocumentEvents(
  events: ChronologyEvent[]
): DocumentEventDb[] {
  return events.map((event) => ({
    id: Math.random(),
    binderId: 'temp',
    documentId: event.sourceDocumentId,
    pageRange: event.sourcePageReferences || '1',
    event: event.title,
    eventType: convertCategoryToEventType(event.category),
    eventDescription: event.summary,
    timestamp: new Date(event.date),
    estimatedTime: false,
    rawExtractedData: {
      ...event,
      provider: event.provider,
      providerSpecialty: event.providerSpecialty,
      department: event.department,
      facility: event.facility
    },
    processed: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }))
}

export function convertChronologyEventsToTimelineEvents(
  events: ChronologyEvent[]
): TimelineEvent[] {
  return events.map((event) => ({
    timestamp: event.date,
    event: event.title,
    eventType: convertCategoryToEventType(event.category),
    eventDescription: event.summary,
    documentId: event.sourceDocumentId,
    pageRange: event.sourcePageReferences || '1',
    // Include provider information
    provider: event.provider,
    providerSpecialty: event.providerSpecialty,
    department:
      event.department || mapEventTypeToDefaultDepartment(event.category),
    facility: event.facility
  }))
}

// Helper function to provide default departments if missing
export function mapEventTypeToDefaultDepartment(
  category: EventCategory
): string {
  const mapping: Record<EventCategory, string> = {
    [EventCategory.PoliceReport]: 'Law Enforcement',
    [EventCategory.AmbulanceReport]: 'EMS',
    [EventCategory.EDAdmission]: 'Emergency Room',
    [EventCategory.EDDischarge]: 'Emergency Room',
    [EventCategory.ImagingDiagnostics]: 'Imaging',
    [EventCategory.Treatment]: 'Medical Treatment',
    [EventCategory.FollowUp]: 'Medical Treatment'
  }
  return mapping[category] || 'Medical Treatment'
}

// Alternative: Post-process with regex if provider info is in description
export function extractProviderFromDescription(
  description: string
): ProviderInfo {
  const result: ProviderInfo = {}

  // Extract doctor names
  const doctorMatch = description.match(
    /(?:Dr\.|Doctor)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)/
  )
  if (doctorMatch) {
    result.provider = doctorMatch[0]
  }

  // Extract department mentions
  const deptPatterns = [
    /Emergency (?:Department|Room|Services)/i,
    /Radiology|Imaging (?:Department|Center)/i,
    /Physical Therapy|Rehabilitation/i,
    /Orthoped(?:ic|ics)/i,
    /Surgery|Operating Room/i
  ]

  for (const pattern of deptPatterns) {
    const match = description.match(pattern)
    if (match) {
      result.department = match[0]
      break
    }
  }

  return result
}

export function convertCategoryToEventType(category: EventCategory): string {
  const mapping: Record<EventCategory, string> = {
    [EventCategory.PoliceReport]: 'police_report',
    [EventCategory.AmbulanceReport]: 'emergency_visit',
    [EventCategory.EDAdmission]: 'emergency_visit',
    [EventCategory.ImagingDiagnostics]: 'diagnostic_test',
    [EventCategory.Treatment]: 'medical_treatment',
    [EventCategory.EDDischarge]: 'emergency_visit',
    [EventCategory.FollowUp]: 'medical_treatment'
  }
  return mapping[category] || 'medical_treatment'
}

// Helper function to find document type for a document ID
export function findDocTypeForDocument(
  documentId: number,
  documents: Record<string, DocumentContent[]>
): string {
  for (const [docType, docs] of Object.entries(documents)) {
    if (docs.some((doc) => doc.id === documentId)) {
      return docType
    }
  }
  return 'unknown'
}

// Memory usage helper function
export function logMemoryUsage(context: string) {
  if (typeof process !== 'undefined' && process.memoryUsage) {
    const memUsage = process.memoryUsage()
    const memInMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    }
    console.log(`🧠 Memory usage at ${context}:`, memInMB)
  }
}

// Helper function to estimate token count (rough approximation)
export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4)
}
