// app/api/gpt/ingest/process-upload/route.ts

import { NextRequest, NextResponse } from 'next/server'
import { UnauthorizedError } from '@/lib/exceptions'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { mapDocumentToCase } from '@/lib/recordstore-case'
import { getEventType, EVENT_TYPES } from '@/lib/utils/queue-event-types'

export const maxDuration = 300

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!(session && session.user)) {
    throw new UnauthorizedError()
  }

  try {
    const user = session.user
    const { s3Key, fileName, fileType, binderId, ref } = await req.json()

    if (!s3Key || !fileName || !fileType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    const document = await db.documentRecords.create({
      data: {
        source: user.teamId!,
        ref: ref,
        title: fileName,
        html: '',
        content: '',
        region: user.region,
        meta: '',
        url: '',
        indexed: false
      }
    })

    if (binderId && binderId !== '') {
      await mapDocumentToCase({
        documentId: document.id,
        binderId: binderId,
        userId: user.id
      })
    }

    await db.queuedEventProcess.create({
      data: {
        type: getEventType(EVENT_TYPES.DOCUMENT_PARSE),
        payload: JSON.stringify({
          documentId: document.id,
          s3Key,
          fileName,
          fileType,
          binderId,
          ref
        })
      }
    })

    return NextResponse.json({ ok: true }, { status: 200 })
  } catch (e: any) {
    // Handle general errors
    console.error('Document processing error:', e)
    return NextResponse.json(
      { error: e.message || 'Unknown error' },
      { status: 500 }
    )
  }
}
