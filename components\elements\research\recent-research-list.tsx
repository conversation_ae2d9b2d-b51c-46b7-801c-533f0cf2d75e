import { formatDate } from '@/lib/utils'
import { ResearchStore } from '@prisma/client'
import Link from 'next/link'

export function RecentResearchList({
  researchHistory,
  path
}: {
  researchHistory: Pick<ResearchStore, 'id' | 'createdAt' | 'question'>[]
  path: string
}) {
  return (
    <div>
      <h2 className="text-2xl font-semibold mb-3">Recent Research</h2>
      <div className="divide-y divide-border rounded-md border bg-background xdark:bg-slate-950 overflow-hidden">
        {researchHistory.map((research, index) => {
          const question = research.question
          const questionPreview =
            question && question.length > 150
              ? question.slice(0, 150) + '...'
              : question || 'Unused research'

          return (
            <Link
              href={`${path}/${research.id}`}
              className="flex items-center justify-between p-4 hover:bg-ring/80 xdark:hover:bg-accent duration-200 cursor-pointer"
              key={index}
            >
              <div className="grid gap-1">
                <p className="font-semibold">{questionPreview}</p>
                <div>
                  <p className="text-sm">
                    {formatDate(research.createdAt?.toDateString())}
                  </p>
                </div>
              </div>
            </Link>
          )
        })}
      </div>
    </div>
  )
}
