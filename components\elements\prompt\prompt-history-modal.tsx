'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>etDescription,
  <PERSON>etHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Clock, FileText } from 'lucide-react'
import { Prompt } from '@prisma/client'

interface PromptHistoryData {
  current: Prompt & {
    version: number
  }
  history: Array<{
    id: number
    source: string
    role: string
    prompt: string
    expectedOutput: string | null
    variables: any
    createdAt: string
  }>
}

interface PromptHistoryModalProps {
  promptId: string
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function PromptHistoryModal({
  promptId,
  open,
  onOpenChange
}: PromptHistoryModalProps) {
  const [historyData, setHistoryData] = useState<PromptHistoryData | null>(null)
  const [selectedHistoryId, setSelectedHistoryId] = useState<
    number | 'current' | null
  >(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (open && promptId) {
      setLoading(true)
      fetch(`/api/prompts/${promptId}/history`)
        .then((res) => res.json())
        .then((data) => {
          setHistoryData(data)
          setSelectedHistoryId('current') // Default to showing current version
        })
        .catch(console.error)
        .finally(() => setLoading(false))
    }
  }, [open, promptId])

  const getSelectedItem = () => {
    if (!historyData) return null
    if (selectedHistoryId === 'current') return historyData.current
    return historyData.history.find((item) => item.id === selectedHistoryId)
  }

  const selectedItem = getSelectedItem()

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="w-full sm:max-w-6xl">
        <SheetHeader>
          <SheetTitle>Prompt History</SheetTitle>
          <SheetDescription>
            View the history of changes for this prompt
          </SheetDescription>
        </SheetHeader>

        {loading && (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        )}

        {historyData && !loading && (
          <div className="flex h-full mt-6 gap-4">
            {/* Left Sidebar - History List */}
            <div className="w-1/3 border-r pr-4">
              <h3 className="font-semibold mb-4">Version History</h3>
              <div className="space-y-2">
                {/* Current Version */}
                <div
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedHistoryId === 'current'
                      ? 'bg-blue-50 border-blue-200'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedHistoryId('current')}
                >
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-green-600" />
                    <span className="font-medium text-green-600">
                      Current (v{historyData.current.version})
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {new Date(historyData.current.updatedAt).toLocaleDateString(
                      'en-US',
                      {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      }
                    )}
                  </div>
                </div>

                {/* History Versions */}
                {historyData.history.map((item, index) => (
                  <div
                    key={item.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedHistoryId === item.id
                        ? 'bg-blue-50 border-blue-200'
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedHistoryId(item.id)}
                  >
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-amber-600" />
                      <span className="font-medium">
                        Version {historyData.history.length - index}
                      </span>
                    </div>
                    <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {new Date(item.createdAt).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Content - Selected Version Details */}
            <div className="flex-1 pl-4">
              {selectedItem && (
                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold text-lg mb-2">
                      {selectedHistoryId === 'current'
                        ? 'Current Version'
                        : `Historical Version`}
                    </h3>
                    <div className="text-sm text-muted-foreground">
                      Source:{' '}
                      <span className="font-mono text-xs">
                        {selectedItem.source}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      Role:{' '}
                      <span className="font-medium">{selectedItem.role}</span>
                    </div>
                    {selectedHistoryId === 'current' && (
                      <div className="text-sm text-muted-foreground">
                        Version:{' '}
                        <span className="font-medium">
                          {historyData.current.version}
                        </span>
                      </div>
                    )}
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Prompt Content</h4>
                    <div className="bg-gray-50 p-4 rounded border font-mono text-sm max-h-64 overflow-y-auto">
                      {selectedItem.prompt}
                    </div>
                  </div>

                  {selectedItem.expectedOutput && (
                    <div>
                      <h4 className="font-medium mb-2">Expected Output</h4>
                      <div className="bg-gray-50 p-4 rounded border font-mono text-sm max-h-32 overflow-y-auto">
                        {selectedItem.expectedOutput}
                      </div>
                    </div>
                  )}

                  {selectedItem.variables &&
                    Object.keys(selectedItem.variables).length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Variables</h4>
                        <div className="bg-gray-50 p-4 rounded border">
                          <pre className="text-sm">
                            {JSON.stringify(selectedItem.variables, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}

                  <div className="text-xs text-muted-foreground pt-4 border-t">
                    {selectedHistoryId === 'current'
                      ? 'Last updated'
                      : 'Created'}
                    :{' '}
                    {new Date(
                      selectedHistoryId === 'current'
                        ? historyData.current.updatedAt
                        : selectedItem.createdAt
                    ).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit'
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {historyData && !loading && historyData.history.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No history records found for this prompt.
          </div>
        )}
      </SheetContent>
    </Sheet>
  )
}
