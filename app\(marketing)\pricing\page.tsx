export const metadata = {
  title: 'Pricing'
}

export default function PricingPage() {
  return (
    <section className="container flex flex-col  gap-6 py-8 md:max-w-[64rem] md:py-12 lg:py-24">
      <div className="mx-auto flex w-full flex-col gap-4 md:max-w-[58rem]">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          Simple, transparent pricing
        </h2>
        {/* <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
          Unlock all features including Legal Research, Deposition Preparation,
          Document Upload, Contract Revision and contextual database search.
        </p> */}
      </div>

      {/* <div className="grid w-full items-start gap-10 rounded-lg border p-10">
        <div className="grid gap-6">
          <h3 className="text-xl font-bold sm:text-2xl">
            What&apos;s included in the PRO monthly plan
          </h3>
          <ul className="grid gap-3 text-sm text-muted-foreground sm:grid-cols-2">
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> 600 queries per month
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> 50 documents per month
            </li>

            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> Unlimited query history
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> Dashboard Analytics
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> Access to Judgement
              Reports
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> Premium Support
            </li>
          </ul>
        </div>
        <div className="flex flex-col gap-4 text-center">
          <div>
            <h4 className="text-7xl font-bold">₹2500</h4>
            <p className="text-sm font-medium text-muted-foreground mt-4">
              Billed Monthly
            </p>
          </div>
          <Link href="/register" className={cn(buttonVariants({ size: 'lg' }))}>
            Start Free Trial
          </Link>
        </div>
      </div>

      <div className="grid w-full items-start gap-10 rounded-lg border p-10">
        <div className="grid gap-6">
          <h3 className="text-xl font-bold sm:text-2xl">
            What&apos;s included in the PRO yearly plan
          </h3>
          <ul className="grid gap-3 text-sm text-muted-foreground sm:grid-cols-2">
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> 600 queries per month
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> 50 documents per month
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> Unlimited query history
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> Dashboard Analytics
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> Access to Judgement
              Reports
            </li>
            <li className="flex items-center">
              <Icons.check className="mr-2 h-4 w-4" /> Premium Support
            </li>
          </ul>
        </div>
        <div className="flex flex-col gap-4 text-center">
          <div>
            <h4 className="text-7xl font-bold">₹20,000</h4>
            <p className="text-sm font-medium text-muted-foreground mt-4">
              Billed Anually
            </p>
          </div>
          <Link href="/register" className={cn(buttonVariants({ size: 'lg' }))}>
            Start Free Trial
          </Link>
        </div>
      </div> */}

      <div className="mx-auto flex w-full max-w-[58rem] flex-col gap-4">
        <p className="max-w-[85%] leading-normal text-amber-900 sm:leading-7">
          For upgrading to a paid plan, please reach out to us over at{' '}
          <strong>
            <a
              href="mailto:<EMAIL>"
              className="underline text-amber-900 xdark:text-blue-400"
            >
              <EMAIL>
            </a>
          </strong>
        </p>
      </div>
      <div className="mx-auto flex w-full max-w-[58rem] flex-col gap-4">
        <p className="max-w-[85%] leading-normal text-amber-900 sm:leading-7">
          <strong>
            Our AI models extend from OpenAI and are thoroughly reviewed by
            legal experts. If you come across any disparities in our results,
            please reach out to us.
          </strong>
        </p>
      </div>
    </section>
  )
}
