import { redirect } from 'next/navigation'
import { adminConfig } from '@/config/dashboard'
import { getCurrentUser } from '@/lib/session'
import { MainNav } from '@/components/elements/layout/main-nav'
import { DashboardNav } from '@/components/elements/layout/nav'
import { SiteFooter } from '@/components/elements/layout/site-footer'
import { UserAccountNav } from '@/components/elements/user/user-account-nav'
import { DashboardHeader } from '@/components/elements/layout/header'
import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import Link from 'next/link'

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default async function DashboardLayout({
  children
}: DashboardLayoutProps) {
  const user = await getCurrentUser()

  if (!user || user.userType !== 'super') {
    redirect('/login')
  }
  return (
    <div className="flex min-h-screen flex-col space-y-6">
      <header className="sticky top-0 z-40 border-b bg-[#1c2519]">
        <div className="container flex h-16 items-center justify-between py-4">
          <MainNav items={adminConfig.mainNav} home={adminConfig.rootPath} />
          <div className="flex items-center gap-5">
            <Link
              className={cn(
                buttonVariants({
                  variant: 'outline',
                  size: 'sm'
                })
              )}
              href="/dashboard"
            >
              User View
            </Link>
            <UserAccountNav user={user} />
          </div>
        </div>
      </header>
      <div className="container grid flex-1 gap-12 md:grid-cols-[200px_1fr]">
        <aside className="hidden w-[200px] flex-col md:flex gap-5">
          {/* <DashboardHeader heading="Admin" /> */}
          <div>
            <DashboardNav items={adminConfig.sidebarNav} />
          </div>
        </aside>
        <main className="flex w-full flex-1 flex-col overflow-hidden">
          {children}
        </main>
      </div>
      <SiteFooter className="border-t bg-[#1c2519]" />
    </div>
  )
}
