import { LegalQuery } from '@/components/elements/chat/legal-query'
import { ExecutiveOrdersResearchInfoCard } from '@/components/elements/custom-components/feature-info-card'
import { getMasqueradeUserNonNullable } from '@/lib/session'
import { ResearchType } from '@prisma/client'
import { Metadata } from 'next'
import Image from 'next/image'

function ExecutiveOrderTable() {
  const executiveOrders = [
    {
      title: 'Initial Rescissions of Harmful Executive Orders and Actions',
      dateSigned: 'January 20, 2025',
      description:
        'Rescinds various orders considered harmful or counter to the administration’s vision.'
    },
    {
      title: 'Restoring Freedom of Speech and Ending Federal Censorship',
      dateSigned: 'January 20, 2025',
      description:
        'Ensures that federal agencies and departments cannot censor protected speech.'
    },
    {
      title: 'Ending the Weaponization of the Federal Government',
      dateSigned: 'January 20, 2025',
      description:
        'Prevents the use of federal agencies for partisan or political purposes.'
    },
    {
      title: 'Putting America First In International Environmental Agreements',
      dateSigned: 'January 20, 2025',
      description:
        'Prioritizes America’s economic and national interests in global environmental treaties.'
    },
    {
      title:
        'Application of Protecting Americans from Foreign Adversary Controlled Applications Act to TikTok',
      dateSigned: 'January 20, 2025',
      description:
        'Grants TikTok a 75-day pause and sets new guidelines to mitigate foreign data security risks.'
    },
    {
      title: 'Withdrawing the United States from the World Health Organization',
      dateSigned: 'January 20, 2025',
      description:
        'Formally initiates the process to remove the U.S. from the WHO.'
    },
    {
      title:
        'Restoring Accountability to Policy-Influencing Positions Within the Federal Workforce',
      dateSigned: 'January 20, 2025',
      description:
        'Reinstates enhanced accountability measures for certain federal positions.'
    },
    {
      title:
        'Holding Former Government Officials Accountable For Election Interference And Improper Disclosure Of Sensitive Governmental Information',
      dateSigned: 'January 20, 2025',
      description:
        'Establishes rules and possible penalties for officials who abuse insider information.'
    },
    {
      title:
        "Clarifying The Military's Role In Protecting The Territorial Integrity Of The United States",
      dateSigned: 'January 20, 2025',
      description:
        'Reaffirms the U.S. military’s capabilities to assist in border and territorial security.'
    },
    {
      title: 'Unleashing American Energy',
      dateSigned: 'January 20, 2025',
      description:
        'Removes barriers to domestic energy production and streamlines permitting processes.'
    },
    {
      title: 'Realigning the United States Refugee Admissions Program',
      dateSigned: 'January 20, 2025',
      description:
        'Adjusts the scope and scale of refugee admissions to align with national security interests.'
    },
    {
      title: 'Protecting the Meaning and Value of American Citizenship',
      dateSigned: 'January 20, 2025',
      description:
        'Initiates steps to end automatic birthright citizenship for certain groups.'
    },
    {
      title: 'Securing Our Borders',
      dateSigned: 'January 20, 2025',
      description:
        'Declares a national emergency and deploys U.S. military assets to the southern border.'
    },
    {
      title: 'Restoring The Death Penalty And Protecting Public Safety',
      dateSigned: 'January 20, 2025',
      description:
        'Revisits federal guidelines and enforcement regarding the death penalty.'
    },
    {
      title: 'Declaring a National Energy Emergency',
      dateSigned: 'January 20, 2025',
      description:
        'Designates certain energy shortfalls as national emergencies to expedite solutions.'
    },
    {
      title: 'Reevaluating And Realigning United States Foreign Aid',
      dateSigned: 'January 20, 2025',
      description:
        'Places new conditions on foreign aid to ensure it aligns with America First principles.'
    },
    {
      title: 'Protecting The American People Against Invasion',
      dateSigned: 'January 20, 2025',
      description:
        'Strengthens measures against illegal immigration, emphasizing national sovereignty.'
    },
    {
      title: "Unleashing Alaska's Extraordinary Resource Potential",
      dateSigned: 'January 20, 2025',
      description:
        'Opens additional federal lands for resource extraction in Alaska.'
    },
    {
      title:
        'Protecting The United States From Foreign Terrorists And Other National Security And Public Safety Threats',
      dateSigned: 'January 20, 2025',
      description:
        'Designates specific cartels and terror groups with stricter travel and financial restrictions.'
    },
    {
      title: 'America First Policy Directive To The Secretary Of State',
      dateSigned: 'January 20, 2025',
      description:
        'Orders the Secretary of State to prioritize American interests in diplomatic engagements.'
    },
    {
      title:
        "Establishing And Implementing The President's Department Of Government Efficiency",
      dateSigned: 'January 20, 2025',
      description:
        'Creates a new department aimed at reducing bureaucratic waste and inefficiency.'
    },
    {
      title:
        'Defending Women from Gender Ideology Extremism and Restoring Biological Truth to the Federal Government',
      dateSigned: 'January 20, 2025',
      description:
        'Rolls back recognition of genders outside male and female in federal documents.'
    },
    {
      title:
        'Ending Radical And Wasteful Government DEI Programs And Preferencing',
      dateSigned: 'January 20, 2025',
      description:
        'Abolishes certain diversity, equity, and inclusion mandates within federal agencies.'
    },
    {
      title:
        'Reforming The Federal Hiring Process And Restoring Merit To Government Service',
      dateSigned: 'January 20, 2025',
      description:
        'Emphasizes merit-based hiring and reduces certain federal employment preferences.'
    },
    {
      title:
        'Designating Cartels And Other Organizations As Foreign Terrorist Organizations And Specially Designated Global Terrorists',
      dateSigned: 'January 20, 2025',
      description:
        'Labels key criminal groups as foreign terrorists to increase enforcement and sanction options.'
    },
    {
      title: 'Restoring Names That Honor American Greatness',
      dateSigned: 'January 20, 2025',
      description:
        'Reverses certain renaming of military bases or public buildings to preserve historical names.'
    },
    {
      title:
        'Ending Illegal Discrimination And Restoring Merit-Based Opportunity',
      dateSigned: 'January 21, 2025',
      description:
        'Further clarifies non-discrimination based on race, gender, or political viewpoints.'
    }
  ]

  return (
    <table className="w-full text-left border-collapse bg-white xdark:bg-slate-900 shadow-sm">
      <thead className="border-b bg-muted">
        <tr>
          <th className="p-4">Title</th>
          <th className="p-4">Date Signed</th>
          <th className="p-4">Description</th>
        </tr>
      </thead>
      <tbody>
        {executiveOrders.map((eo, i) => (
          <tr
            key={i}
            className="border-b hover:bg-gray-50 xdark:hover:bg-slate-800"
          >
            <td className="p-4 font-semibold">{eo.title}</td>
            <td className="p-4">{eo.dateSigned}</td>
            <td className="p-4">{eo.description}</td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}

export const metadata: Metadata = {
  title: 'List of Executive Orders in the Second Presidency of Donald Trump',
  description:
    'Explore the executive orders signed during Donald Trump’s second presidency. Highlights, policy directions, and what to look forward to under the new administration.'
}

export default async function SecondTrumpPresidencyPage() {
  const user = await getMasqueradeUserNonNullable('clq901grk000039n1wgxccaek')

  return (
    <main className="space-y-6 pb-8 pt-6 md:pb-12 md:pt-10 lg:py-32">
      {/* HERO SECTION */}
      <section className="container flex max-w-[64rem] flex-col items-center gap-4 text-center py-12">
        <div className="text-center space-y-4">
          <div className="rounded-2xl bg-red-600 px-4 py-1.5 text-sm font-medium text-white w-max m-auto">
            Trump 2.0
          </div>
          <h1 className="font-heading text-3xl sm:text-5xl md:text-6xl lg:text-7xl text-red-600">
            The Second Presidency <br /> of Donald J. Trump
          </h1>
          <p className="mx-auto pt-4 leading-normal text-muted-foreground sm:text-xl sm:leading-8 max-w-3xl">
            Examine the key decisions and Executive Orders enacted during Donald
            Trump&apos;s second term and their effects on life in the United
            States. This tool helps you grasp how these policies influence
            economic opportunities, public services, safety, and individual
            freedoms.
          </p>
        </div>
      </section>

      {/* INTRODUCTION */}
      <section className="container lg:grid lg:grid-cols-5 gap-16 items-center space-y-6 bg-slate-50 py-8 xdark:bg-transparent md:py-12 lg:py-24">
        <div className="space-y-6 lg:col-span-3">
          <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
            Understanding Policy Shifts in America
          </h2>
          <p className="leading-normal text-muted-foreground sm:text-xl sm:leading-8">
            Donald Trump&apos;s second term brought executive orders addressing
            sovereignty, free speech, and security, along with changes in
            international agreements and border policies. The administration
            emphasized government efficiency, economic reforms, and certain
            values, offering insights into the broader implications of these
            strategies for citizens.
          </p>
        </div>
        <Image
          src="/marketing/logos/america-flag.jpg"
          alt="President Donald Trump signing an executive order"
          width={1200}
          height={800}
          className="rounded-lg hidden lg:block lg:col-span-2"
        />
      </section>

      <section className="container space-y-6 bg-slate-50 py-8 xdark:bg-transparent md:py-12 lg:py-24">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          Keen to Learn?
        </h2>
        <div className="grid gap-10">
          <LegalQuery
            researchProps={{
              model: 'brainstem-trumerica',
              sources: [],
              court: ['executive_orders_trump_2'],
              year: [],
              sourcesForMessages: {}
            }}
            user={user}
            researchType={ResearchType.law}
            showFilters={false}
            emptyStateComponent={<ExecutiveOrdersResearchInfoCard />}
            masquerade={true}
          />
        </div>
      </section>

      {/* EXECUTIVE ORDER TABLE */}
      <section className="container space-y-6 py-8 xdark:bg-transparent md:py-12 lg:py-24">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          Executive Orders 2025
        </h2>
        <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8 mb-8">
          Below is a comprehensive listing of President Trump’s Executive
          Orders, beginning January 20, 2025. Each order has implications for
          domestic policy, national security, or America’s standing on the world
          stage.
        </p>
        <ExecutiveOrderTable />
      </section>

      {/* WHAT TO LOOK FORWARD TO / HIGHLIGHTS SECTION */}
      <section className="container space-y-6 bg-slate-50 py-8 xdark:bg-transparent md:py-12 lg:py-24">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          What to Expect
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Highlight 1 */}
          <div className="relative overflow-hidden rounded-lg border bg-background p-2">
            <div className="flex h-full flex-col justify-between rounded-md p-6">
              <div className="space-y-2">
                <h3 className="font-bold">National Sovereignty</h3>
                <p className="text-sm text-muted-foreground">
                  Policies focusing on border management, the role of the U.S.
                  military, and constitutional approaches to immigration.
                </p>
              </div>
            </div>
          </div>

          {/* Highlight 2 */}
          <div className="relative overflow-hidden rounded-lg border bg-background p-2">
            <div className="flex h-full flex-col justify-between rounded-md p-6">
              <div className="space-y-2">
                <h3 className="font-bold">Economic Development</h3>
                <p className="text-sm text-muted-foreground">
                  Executive actions aimed at enhancing domestic energy
                  production, simplifying regulations, and negotiating trade
                  agreements that prioritize national interests.
                </p>
              </div>
            </div>
          </div>

          {/* Highlight 3 */}
          <div className="relative overflow-hidden rounded-lg border bg-background p-2">
            <div className="flex h-full flex-col justify-between rounded-md p-6">
              <div className="space-y-2">
                <h3 className="font-bold">Government Efficiency</h3>
                <p className="text-sm text-muted-foreground">
                  Initiatives to streamline federal operations, reduce waste,
                  and improve public service delivery through new administrative
                  measures.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CLOSING / SIGN UP OR CALL TO ACTION SECTION */}
      <section className="container space-y-6 py-8 xdark:bg-transparent md:py-12 lg:py-24">
        <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
          Join the Conversation
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 items-center">
          <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8 col-span-3">
            Stay informed about recent policy changes and their impact on
            America&apos;s future. Participate in discussions, sign up for
            updates, and explore opportunities to contribute your perspective on
            these pivotal decisions. Engage with the community to better
            understand how these developments shape the nation and its citizens.
          </p>
          <div className="flex justify-center">
            {/* Example button with Tailwind classes */}
            <a
              href="/register"
              className="inline-flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-white hover:bg-red-700 font-medium"
            >
              Sign Up for Updates
            </a>
          </div>
        </div>
      </section>
    </main>
  )
}
