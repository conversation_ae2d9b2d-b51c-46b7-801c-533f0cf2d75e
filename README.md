# Smart Counsel AI

**Purpose-built AI for Personal Injury and Mass Tort Law Firms**

Smart Counsel AI is a comprehensive legal technology platform that harnesses artificial intelligence to streamline legal workflows, enhance case management, and provide precise insights for personal injury and mass tort practices.

![Smart Counsel AI](public/brand/logo-full.png)

## 🚀 Features

### Core AI Capabilities
- **🔍 Legal Research & Analysis**: Access extensive databases with AI-powered insights for case law and legal precedents
- **📄 Document Review & Analysis**: Automated review with intelligent suggestions and handwritten document recognition
- **⚖️ Case Evaluation**: Comprehensive AI-driven case assessment including liability, damages, and risk analysis
- **📝 Medical Chronology Generation**: Automated medical timeline creation with event extraction and deduplication
- **✏️ Demand Letter Drafting**: AI-powered generation of compelling demand letters
- **🔒 Intelligent Redaction**: Bulk document redaction with privacy compliance and accuracy
- **👥 Juror Evaluation**: AI analysis of juror profiles and social media for bias detection
- **💾 Secure Cloud Storage**: HIPAA-compliant document storage with collaboration features

### Advanced Features
- **Evidence Management**: Organize, categorize, and retrieve case exhibits efficiently
- **Multi-Provider LLM Support**: Integration with OpenAI, Azure OpenAI, and Google Gemini
- **Token Usage Tracking**: Comprehensive monitoring of AI service consumption
- **Team Collaboration**: Multi-user support with role-based access control
- **Real-time Notifications**: Slack integration for case processing updates

## 🛠️ Technology Stack

- **Framework**: Next.js 14 with App Router
- **Database**: MySQL with Prisma ORM (PostgreSQL support available)
- **Authentication**: NextAuth.js with Google OAuth and credentials
- **AI/LLM**: OpenAI GPT-4, Azure OpenAI, Google Gemini
- **Vector Database**: Pinecone for semantic search
- **File Storage**: AWS S3 integration
- **Document Processing**: Azure Document Intelligence
- **UI Components**: Radix UI with Tailwind CSS
- **Rich Text Editor**: TipTap with custom extensions

## 📋 Prerequisites

- **Node.js**: Version 20.x or higher
- **npm**: Latest version
- **MySQL Database**: Version 8.0+ (or PostgreSQL 13+)
- **Required API Keys**:
  - OpenAI API Key
  - Google OAuth credentials
  - Azure OpenAI (optional)
  - Google Gemini API Key (optional)
  - Pinecone API Key
  - AWS S3 credentials
  - Postmark API token for emails

## 🔧 Installation & Setup

### 1. Clone the Repository
```bash
git clone https://github.com/your-organization/smart-counsel-ai.git
cd smart-counsel-ai
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration

Create a `.env.local` file in the root directory with the following variables:

```env
# Application
NEXTAUTH_URL=http://localhost:3011
NEXTAUTH_SECRET=your-nextauth-secret-here
NODE_ENV=development

# Database
DATABASE_URL="mysql://user:password@localhost:3306/smartcounsel"
POSTGRESQL_DATABASE_URL="postgresql://user:password@localhost:5432/smartcounsel"

# Authentication
GOOGLE_ID=your-google-oauth-client-id
GOOGLE_SECRET=your-google-oauth-client-secret

# Email (Postmark)
SMTP_FROM=<EMAIL>
POSTMARK_API_TOKEN=your-postmark-api-token
POSTMARK_SIGN_IN_TEMPLATE=your-signin-template-id
POSTMARK_ACTIVATION_TEMPLATE=your-activation-template-id

# AI Services
OPENAI_API_KEY=your-openai-api-key
GEMINI_API_KEY=your-gemini-api-key

# Azure OpenAI (Optional)
AZURE_OPENAI_API_KEY=your-azure-openai-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/

# Vector Database
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX=your-pinecone-index-name

# AWS S3
AWS_KEYID=your-aws-access-key-id
AWS_SECRET=your-aws-secret-access-key
AWS_BUCKET=your-s3-bucket-name
AWS_REGION=your-aws-region

# Slack Integration
SLACK_TOKEN=your-slack-bot-token

# Processing Server
PROCESS_SERVER=http://localhost:3011
```

### 4. Database Setup

For MySQL (default):
```bash
npm run update-db
```

For PostgreSQL:
```bash
npm run migrate-pg
npm run generate-pg
```

### 5. Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:3011`

## 🚀 Production Deployment

### Environment Setup
1. Set `NODE_ENV=production`
2. Configure production database URLs
3. Set secure `NEXTAUTH_SECRET`
4. Update `NEXTAUTH_URL` to your production domain

### Build & Deploy
```bash
# Build the application
npm run build

# Start production server
npm run start
```

### Recommended Deployment Platforms
- **Vercel** (Recommended for Next.js)
- **AWS ECS/EC2** with Load Balancer
- **Google Cloud Run**
- **Azure Container Instances**

### Database Considerations
- Use connection pooling for production
- Set up read replicas for better performance
- Configure automated backups
- Monitor query performance

## 📚 API Documentation

### Authentication Endpoints
- `POST /api/auth/signin` - User sign in
- `POST /api/auth/signout` - User sign out
- `GET /api/auth/session` - Get current session

### Core API Routes
- `POST /api/gpt/legal-query` - Legal research queries
- `POST /api/gpt/review` - Document review
- `POST /api/gpt/redactor` - Document redaction
- `POST /api/gpt/assess` - Case evaluation
- `GET /api/documents/download-docx` - Document export
- `POST /api/prompts/by-feature` - Prompt management

### Webhook Endpoints
- `POST /api/cron/case-processor` - Process case queues
- `POST /api/cron/document-processor` - Document processing

## 🔐 Security & Compliance

### Data Protection
- HIPAA-compliant architecture
- End-to-end encryption for sensitive data
- Secure file upload and storage
- Role-based access control

### Authentication & Authorization
- Multi-factor authentication support
- Session management with JWT
- OAuth integration with Google
- Password hashing with bcrypt

### Infrastructure Security
- HTTPS enforcement
- Environment variable protection
- Database connection encryption
- API rate limiting

## 🔧 Configuration

### Feature Flags
Configure features through environment variables:
- `ENABLE_AZURE_OPENAI` - Enable Azure OpenAI integration
- `ENABLE_GEMINI` - Enable Google Gemini
- `ENABLE_SLACK_NOTIFICATIONS` - Enable Slack notifications

### AI Model Configuration
Models can be configured in the application:
- **GPT-4 Turbo** for complex analysis
- **GPT-4 Mini** for quick operations
- **Gemini 2.5 Pro** for specialized tasks

## 📊 Monitoring & Analytics

### Token Usage Tracking
- Monitor AI service consumption
- Track costs per team/user
- Set usage alerts and limits

### Performance Monitoring
- Database query optimization
- API response time tracking
- Error logging and alerting

### Health Checks
- Database connectivity
- External API availability
- File storage accessibility

## 🔄 Development Workflow

### Scripts
```bash
npm run dev          # Development server (port 3011)
npm run build        # Production build
npm run start        # Production server
npm run lint         # ESLint code quality check
npm run prettier     # Format TypeScript files
npm run update-db    # Update database schema
npm run redev        # Clean restart development
```

### Database Management
```bash
npm run migrate-pg   # PostgreSQL migrations
npm run generate-pg  # Generate PostgreSQL client
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -m 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

### Code Standards
- Follow ESLint configuration
- Use Prettier for code formatting
- Write TypeScript with strict types
- Include unit tests for new features

## 📝 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For technical support and inquiries:
- **Email**: <EMAIL>
- **Documentation**: [docs.smartcounselai.com](https://docs.smartcounselai.com)
- **Status Page**: [status.smartcounselai.com](https://status.smartcounselai.com)

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   External      │
│   (Next.js)     │◄──►│   (API Routes)  │◄──►│   Services      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ┌────▼────┐              ┌───▼───┐              ┌────▼────┐
    │ UI/UX   │              │Database│              │   AI    │
    │Components│              │(MySQL) │              │Services │
    └─────────┘              └────────┘              └─────────┘
```

## 🔍 Key Components

### Authentication System
- NextAuth.js with multiple providers
- JWT-based session management
- Role-based access control
- Team-based permissions

### Document Processing Pipeline
1. File upload to AWS S3
2. Azure Document Intelligence extraction
3. Vector embedding generation
4. Pinecone storage for semantic search
5. AI-powered analysis and insights

### Case Management Workflow
1. Document ingestion and processing
2. Medical chronology generation
3. Case evaluation and risk assessment
4. Demand letter creation
5. Evidence organization and retrieval

---

**Smart Counsel AI** - Empowering legal professionals with intelligent technology for better case outcomes.
