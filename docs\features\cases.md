# Cases

The Cases feature in LexLumen provides comprehensive case management capabilities for legal professionals, allowing them to create, organize, and manage legal cases with AI-powered tools.

## Overview

Cases (formerly called "Binders") serve as containers for organizing all case-related documents, research, and AI-generated content. Each case can include multiple specialized features for legal document analysis and generation.

## Key Features

### Case Creation and Management
- **Create New Cases**: Users can create new cases with custom names and descriptions
- **Document Organization**: Upload and organize case documents by type (medical records, police reports, contracts, etc.)
- **Document Polling**: Real-time updates when documents are being processed in the background
- **Case Navigation**: Easy navigation between different case features through the dashboard

### Document Upload and Processing
- **Multi-format Support**: Upload various document formats (PDF, DOCX, images)
- **Automatic Processing**: Documents are automatically processed using Azure Document Intelligence
- **Content Extraction**: Text and HTML content extracted for AI analysis
- **Document Categorization**: Documents can be categorized by type for better organization

### Research Integration
- **Case-Specific Research**: Conduct research within the context of a specific case
- **Document-Based Queries**: Ask questions about uploaded documents
- **Research History**: Track all research conducted for a case
- **Cross-Reference**: Link research findings with case documents

## Inner Case Features

Cases provide access to specialized AI-powered legal tools:

1. **[Medical Chronology](./medical-chronology.md)** - Generate structured timelines from medical records
2. **[Case Evaluation](./case-evaluation.md)** - AI-driven case assessment and damage calculation
3. **[Demand Letter Generation](./demand-letter-generation.md)** - Automated demand letter creation

## Navigation and Access

### Dashboard Access
- Navigate to `/dashboard/case` from the main dashboard
- View all cases in a list format with recent activity
- Click on any case to access its details and features

### Case Dashboard
Each case has its own dashboard with:
- Overview of case information
- Quick access to all case features
- Document management interface
- Recent activity feed

## Technical Implementation

### Database Structure
```typescript
// Core case model (Binder in database)
model Binder {
  id          String   @id @default(cuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  userId      String
  teamId      String
  
  // Relationships
  CaseFile         CaseFile[]
  ResearchStore    ResearchStore[]
  DocumentDataset  Dataset[]
}

// Case files for different features
model CaseFile {
  id                        String       @id @default(cuid())
  binderId                  String
  fileType                  CaseFileType
  content                   String?
  selectedDocumentsByType   Json?
  createdAt                 DateTime     @default(now())
  updatedAt                 DateTime     @updatedAt
}
```

### Credit System
- Each case feature consumes credits when used
- Usage tracking prevents overuse of AI features
- Credit consumption is recorded per team

### Processing Pipeline
1. **Document Upload**: Documents uploaded through UI
2. **Background Processing**: Azure Document Intelligence extracts content
3. **Database Storage**: Content stored with metadata
4. **AI Processing**: Features use extracted content for analysis
5. **Result Storage**: Generated content stored as CaseFile records

## User Interface

### Case List View
- Grid or list view of all user cases
- Search and filter capabilities
- Quick actions for each case
- Creation date and last activity

### Case Detail View
- Case information and metadata
- Document management interface
- Feature access buttons
- Activity timeline

### Document Management
- Drag-and-drop upload interface
- Document type categorization
- Processing status indicators
- Preview and download capabilities

## Best Practices

### Case Organization
- Use descriptive case names
- Categorize documents properly
- Regular cleanup of unused documents
- Maintain consistent naming conventions

### Document Management
- Upload complete document sets before using AI features
- Ensure document quality for better AI results
- Use appropriate document types for each feature
- Monitor processing status before proceeding

### Feature Usage
- Review credit limits before extensive usage
- Use appropriate features for specific needs
- Save important generated content
- Regular backup of case data

## API Endpoints

### Case Management
- `GET /api/cases` - List user cases
- `POST /api/cases` - Create new case
- `PUT /api/cases/[id]` - Update case
- `DELETE /api/cases/[id]` - Delete case

### Document Management
- `POST /api/cases/[id]/documents` - Upload documents
- `GET /api/cases/[id]/documents` - List case documents
- `DELETE /api/documents/[id]` - Delete document

### Feature Access
- `POST /api/cases/[id]/medical-chronology` - Generate medical chronology
- `POST /api/cases/[id]/case-evaluation` - Generate case evaluation
- `POST /api/cases/[id]/demand-letter` - Generate demand letter

## Error Handling

### Common Issues
- **Document Processing Failures**: Retry mechanism for failed uploads
- **Credit Exhaustion**: Clear messaging when credits are depleted
- **Large File Handling**: Progress indicators for large uploads
- **Network Issues**: Offline handling and retry logic

### Error Recovery
- Automatic retry for transient failures
- Manual retry options for users
- Clear error messages with suggested actions
- Support contact information for complex issues

## Integration with Other Features

### Research Features
- Cases integrate with all research types
- Research can be saved to specific cases
- Case documents enhance research context

### Admin Features
- Admin users can view all cases
- Masquerade functionality for testing
- Usage analytics and reporting

### Team Collaboration
- Team members can access shared cases
- Permission-based access control
- Activity tracking for team coordination 