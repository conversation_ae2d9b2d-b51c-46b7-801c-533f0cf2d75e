import type { NextApiRequest, NextApiResponse } from 'next'
import { db } from '@/lib/db'
import { QueuedEventStatus } from '@prisma/client'
import { handleMedicalChronologyGeneration } from '@/lib/actions/case/med-cron'
import {
  getEventTypeVariations,
  getBaseEventType,
  EVENT_TYPES
} from '@/lib/utils/queue-event-types'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // console.log('🚀 Starting case processing cron job')
    const startTime = Date.now()
    const timeLimit = 55000 // 55 seconds in milliseconds
    let processedCases = 0

    while (Date.now() - startTime < timeLimit) {
      // console.log('📊 Fetching pending case parsing requests')
      // Find pending case parsing requests (all case types)
      const allCaseEventTypes = [
        ...getEventTypeVariations(EVENT_TYPES.MEDICAL_CHRONOLOGY)
      ]

      const ongoing = await db.queuedEventProcess.count({
        where: {
          type: {
            in: allCaseEventTypes
          },
          status: QueuedEventStatus.processing
        }
      })

      const loadableSize = ongoing < 1 ? 1 - ongoing : 0
      if (loadableSize === 0) {
        // console.log('✅ No loadable size available, exiting')
        break
      }

      const newCases = await db.queuedEventProcess.findMany({
        where: {
          type: {
            in: allCaseEventTypes
          },
          status: QueuedEventStatus.pending
        },
        orderBy: {
          createdAt: 'asc'
        },
        take: loadableSize
      })

      // console.log(`📋 Found ${newCases.length} pending case(s) to process`)
      // Exit loop if no pending cases remain
      if (newCases.length === 0) {
        await new Promise((resolve) => setTimeout(resolve, 3000))
        // console.log('✅ No pending cases found, exiting')
        break
      }

      // console.log('🔄 Marking cases as processing')
      // Mark these cases as processing
      await db.queuedEventProcess.updateMany({
        where: {
          id: {
            in: newCases.map((caseItem) => caseItem.id)
          }
        },
        data: {
          status: QueuedEventStatus.processing
        }
      })

      // Process each case
      for (const caseItem of newCases) {
        const { payload, user } = JSON.parse(caseItem.payload)
        // console.log(
        //   `⏳ Processing case: ${payload.fileName} (ID: ${payload.documentId})`
        // )
        const baseEventType = getBaseEventType(caseItem.type)
        switch (baseEventType) {
          case EVENT_TYPES.MEDICAL_CHRONOLOGY:
            await handleMedicalChronologyGeneration({
              processId: caseItem.id,
              payload,
              user
            })
            break

          default:
            console.warn(`Unknown event type: ${baseEventType}`)
            break
        }

        processedCases++

        // wait 3 seconds between each case to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 3000))
      }
    }

    const totalTime = (Date.now() - startTime) / 1000
    // console.log(
    //   `✅ Case processing cron job completed successfully in ${totalTime.toFixed(2)}s`
    // )

    // Generate appropriate response message based on results
    let responseMessage = ''
    if (processedCases === 0) {
      responseMessage = 'No cases to process'
    } else {
      responseMessage = `Successfully processed ${processedCases} case(s) in ${totalTime.toFixed(2)} seconds`
    }

    res.status(200).json({
      message: responseMessage,
      processed: processedCases,
      time: totalTime
    })
  } catch (e: any) {
    console.error('❌ Case processing error:', e)
    res.status(500).json({ error: 'Failed to index.', errorcontent: e })
  }
}
