import { db } from '../../db'
import { createGeminiCompletion } from '../../services/gemini-service'
import { logger } from '../utils-llm'
import {
  ChronologyEvent,
  PlaintiffInfo,
  IncidentDetails,
  DocumentContent,
  CaseGap
} from '@/types/case'
import { GeminiModel } from '@/types'
import { estimateTokenCount } from './utils'
import { MAX_EVENTS_FOR_GAP_ANALYSIS, SAFE_TOKEN_LIMIT } from './constants'
import { AuthUser } from 'next-auth'

// Step 5: Identify legal gaps and case strength
export async function identifyLegalGaps(
  events: ChronologyEvent[],
  plaintiffInfo: PlaintiffInfo,
  incidentDetails: IncidentDetails,
  documents: Record<string, DocumentContent[]>,
  user: AuthUser
): Promise<CaseGap[]> {
  logger.start('identifyLegalGaps')

  try {
    // Limit events to prevent token overflow
    const limitedEvents = events.slice(0, MAX_EVENTS_FOR_GAP_ANALYSIS)

    // Create event summary for gap analysis
    const eventSummary = {
      totalEvents: events.length,
      analyzedEvents: limitedEvents.length,
      eventTimeline: {
        earliest: limitedEvents[0]?.date || 'Unknown',
        latest: limitedEvents[limitedEvents.length - 1]?.date || 'Unknown'
      },
      eventTypeBreakdown: limitedEvents.reduce(
        (acc, event) => {
          acc[event.category] = (acc[event.category] || 0) + 1
          return acc
        },
        {} as Record<string, number>
      ),
      // Include only key events for analysis
      keyEvents: limitedEvents.map((event) => ({
        date: event.date,
        category: event.category,
        title: event.title,
        // Truncate summary to reduce tokens
        summary:
          event.summary.substring(0, 150) +
          (event.summary.length > 150 ? '...' : '')
      }))
    }

    // Prepare the data for Gemini
    const data = {
      eventSummary,
      plaintiffInfo,
      incidentDetails,
      documentTypes: Object.keys(documents),
      documentCount: Object.values(documents).flat().length
    }

    const caseInformation = JSON.stringify(data)
    const estimatedTokens = estimateTokenCount(caseInformation)

    logger.info(
      `📊 Preparing gap analysis with ${limitedEvents.length}/${events.length} events (estimated ${estimatedTokens} tokens)`
    )

    const identifyLegalGapsPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_CASE_GAPS'
      }
    })

    const prompt =
      identifyLegalGapsPrompt!.prompt.replace(
        '{{caseInformation}}',
        caseInformation
      ) + identifyLegalGapsPrompt!.expectedOutput!

    // Check token limit and reduce data if needed
    const promptTokens = estimateTokenCount(prompt)
    if (promptTokens > SAFE_TOKEN_LIMIT) {
      logger.info(
        `⚠️ Token limit warning in identifyLegalGaps: ${promptTokens} tokens exceeds safe limit. Reducing event count.`
      )
      // Reduce events further
      const reducedEvents = limitedEvents.slice(0, 100)
      eventSummary.keyEvents = reducedEvents.map((event) => ({
        date: event.date,
        category: event.category,
        title: event.title,
        summary: event.summary.substring(0, 100) + '...'
      }))
      data.eventSummary = eventSummary
    }

    const response = await createGeminiCompletion({
      modelName: GeminiModel.Gemini25Pro,
      systemInstruction: prompt,
      message: 'Analyze case for legal gaps and strategic opportunities',
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'legal-gap-analysis'
    })

    // Parse the identified gaps from the response
    try {
      const responseData =
        typeof response === 'string' ? JSON.parse(response) : response
      const caseGaps: CaseGap[] = responseData.caseGaps || []

      // Count issues by severity
      const severityCounts = caseGaps.reduce(
        (counts, gap) => {
          counts[gap.severity] = (counts[gap.severity] || 0) + 1
          return counts
        },
        {} as Record<string, number>
      )

      logger.end('identifyLegalGaps', {
        gapCount: caseGaps.length,
        severityCounts,
        eventsAnalyzed: limitedEvents.length
      })

      return caseGaps
    } catch (parseError) {
      logger.error('identifyLegalGaps', response)
      throw new Error('Failed to parse case gaps from AI response')
    }
  } catch (error: any) {
    logger.error('identifyLegalGaps', error)
    throw new Error(`Failed to identify legal gaps: ${error.message}`)
  }
}
