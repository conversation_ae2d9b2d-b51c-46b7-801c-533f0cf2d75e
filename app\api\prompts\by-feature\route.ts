import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

const FEATURE_GROUPS = {
  AI_MEDICAL_CHRONOLOGY: [
    'AI_MEDICAL_CHRONOLOGY_CASE_INFORMATION_METADATA',
    'AI_MEDICAL_CHRONOLOGY_DIAGNOSTIC_HIGHLIGHTS',
    'AI_MEDICAL_CHRONOLOGY_TREATMENT_CALENDAR',
    'AI_MEDICAL_CHRONOLOGY_FLAGS_AND_CASE_GAPS',
    'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE',
    'AI_MEDICAL_CHRONOLOGY_CASE_STRENGTH_ANALYSIS',
    'AI_MEDICAL_CHRONOLOGY_DOCUMENT_EXTRACTION',
    'AI_MEDICAL_CHRONOLOGY_STRUCTURE_METADATA',
    'AI_MEDICAL_CHRONOLOGY_ORGANIZE_EVENTS',
    'AI_MEDICAL_CHRONOLOGY_CASE_GAPS',
    'AI_MEDICAL_CHRONOLOGY_MULTIPLE_INJURY_PIPELINE',
    'AI_MEDICAL_CHRONOLOGY_PROCESS_STRATEGY',
    'AI_MEDICAL_CHRONOLOGY_EVENT_EXTRACTION',
    'AI_MEDICAL_CHRONOLOGY_EVENT_DEDUPLICATION'
  ],
  AI_CASE_EVALUATION: [
    'AI_CASE_EVALUATION_PROCESS_ATTORNEY_INSIGHTS',
    'AI_CASE_EVALUATION_ASSESS_LIABILITY',
    'AI_CASE_EVALUATION_CALCULATE_ECONOMIC_DAMAGES',
    'AI_CASE_EVALUATION_EVALUATE_NONECONOMIC_DAMAGES',
    'AI_CASE_EVALUATION_ASSESS_PUNITIVE_DAMAGES',
    'AI_CASE_EVALUATION_IDENTIFY_CASE_RISKS',
    'AI_CASE_EVALUATION_DEVELOP_LITIGATION_STRATEGY',
    'AI_CASE_EVALUATION_CASE_OVERVIEW',
    'AI_CASE_EVALUATION_LIABILITY_ASSESSMENT',
    'AI_CASE_EVALUATION_DAMAGES_CALCULATION',
    'AI_CASE_EVALUATION_RISK_ANALYSIS',
    'AI_CASE_EVALUATION_LITIGATION_STRATEGY',
    'AI_CASE_EVALUATION_ATTORNEY_INSIGHTS',
    'AI_CASE_EVALUATION_FINAL_RECOMMENDATION',
    'AI_CASE_EVALUATION_ANALYZE_DEFENSE_PERSPECTIVE',
    'AI_CASE_EVALUATION_GENERATE_REVISED_REPORT'
  ],
  AI_DEMAND_LETTER: [
    'AI_DEMAND_LETTER_COVER_LETTER',
    'AI_DEMAND_LETTER_FACTS_AND_LIABILITY',
    'AI_DEMAND_LETTER_INJURIES_AND_TREATMENTS',
    'AI_DEMAND_LETTER_DAMAGES_OVERVIEW',
    'AI_DEMAND_LETTER_PAST_MEDICAL_EXPENSES',
    'AI_DEMAND_LETTER_FUTURE_MEDICAL_EXPENSES',
    'AI_DEMAND_LETTER_LOSS_OF_INCOME',
    'AI_DEMAND_LETTER_LOSS_OF_HOUSEHOLD_SERVICES',
    'AI_DEMAND_LETTER_PAIN_AND_SUFFERING',
    'AI_DEMAND_LETTER_PUNITIVE_DAMAGES',
    'AI_DEMAND_LETTER_SETTLEMENT_DEMAND',
    'AI_DEMAND_LETTER_EXHIBIT_LIST'
  ]
} as const

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const feature = searchParams.get('feature') as keyof typeof FEATURE_GROUPS

    if (!feature || !FEATURE_GROUPS[feature]) {
      return NextResponse.json({ error: 'Invalid feature' }, { status: 400 })
    }

    const prompts = await db.prompt.findMany({
      where: {
        source: {
          in: [...FEATURE_GROUPS[feature]]
        }
      },
      orderBy: { source: 'asc' }
    })

    return NextResponse.json(prompts)
  } catch (error) {
    console.error('Error fetching prompts by feature:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
