'use client'

import React, { useState } from 'react'
import { But<PERSON>, buttonVariants } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'

export const FeatureDisplay = ({
  features
}: {
  features: {
    title: string
    heading: string
    description: string
  }[]
}) => {
  const [selectedFeature, setSelectedFeature] = useState(features[0])

  return (
    <>
      <div className="hidden md:flex flex-wrap gap-4 justify-center">
        {features.map((feature) => (
          <Button
            key={feature.title}
            variant={
              selectedFeature.title === feature.title ? 'default' : 'outline'
            }
            onClick={() => setSelectedFeature(feature)}
            className={cn(
              'py-6 px-6',
              selectedFeature.title === feature.title && 'text-md'
            )}
          >
            {feature.title}
          </Button>
        ))}
      </div>

      <div className="md:hidden w-full flex justify-center mt-4">
        <Select
          value={selectedFeature.title}
          onValueChange={(value) => {
            const feature = features.find((f) => f.title === value)
            if (feature) setSelectedFeature(feature)
          }}
        >
          <SelectTrigger className="w-full font-bold text-md gap-2 flex justify-center items-center bg-amber-200 xdark:bg-amber-400 rounded-full py-6">
            <SelectValue placeholder="Select a feature" />
          </SelectTrigger>
          <SelectContent>
            {features.map((feature) => (
              <SelectItem key={feature.title} value={feature.title}>
                {feature.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex gap-4 items-center">
        <div className="mt-8 text-left max-w-3xl space-y-7">
          <h2 className="text-3xl lg:text-5xl font-bold">
            {selectedFeature.heading}
          </h2>
          <p className="text-lg">{selectedFeature.description}</p>
          <Link href="/register" className={cn(buttonVariants({ size: 'lg' }))}>
            Get Started
          </Link>
        </div>
        <Image
          src="/marketing/logos/ui-skeleton.png"
          alt="UI Skeleton"
          className="hidden lg:block"
          width={800}
          height={800}
        />
      </div>
    </>
  )
}
