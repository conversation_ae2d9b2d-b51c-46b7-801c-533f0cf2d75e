import type { NextApiRequest, NextApiResponse } from 'next'
import { db } from '@/lib/db'
import { storePrivateTextEmbedding } from '@/lib/pg-db'
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter'
import { AWS_BUCKET, s3DownloadFile, getS3Url } from '@/lib/services/s3-service'
import { QueuedEventStatus } from '@prisma/client'
import { extractFileContentAndHtmlWithAzure } from '@/lib/services/azure-docintelligence.service'
import {
  getEventTypeVariations,
  EVENT_TYPES
} from '@/lib/utils/queue-event-types'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // console.log('🚀 Starting document processing cron job')
    const startTime = Date.now()
    const timeLimit = 55000 // 55 seconds in milliseconds
    let processedDocuments = 0

    while (Date.now() - startTime < timeLimit) {
      //   console.log('📊 Fetching pending document parsing requests')
      // Find pending document parsing requests
      const ongoing = await db.queuedEventProcess.count({
        where: {
          type: {
            in: getEventTypeVariations(EVENT_TYPES.DOCUMENT_PARSE)
          },
          status: QueuedEventStatus.processing
        }
      })

      const loadableSize = ongoing < 5 ? 5 - ongoing : 0
      if (loadableSize === 0) {
        // console.log('✅ No loadable size available, exiting')
        break
      }

      const newUploads = await db.queuedEventProcess.findMany({
        where: {
          type: {
            in: getEventTypeVariations(EVENT_TYPES.DOCUMENT_PARSE)
          },
          status: QueuedEventStatus.pending
        },
        orderBy: {
          createdAt: 'asc'
        },
        take: loadableSize
      })

      //   console.log(
      //     `📋 Found ${newUploads.length} pending document(s) to process`
      //   )
      // Exit loop if no pending uploads remain
      if (newUploads.length === 0) {
        await new Promise((resolve) => setTimeout(resolve, 3000))
        // console.log('✅ No pending documents found, exiting')
        break
      }

      //   console.log('🔄 Marking documents as processing')
      // Mark these uploads as processing
      await db.queuedEventProcess.updateMany({
        where: {
          id: {
            in: newUploads.map((upload) => upload.id)
          }
        },
        data: {
          status: QueuedEventStatus.processing
        }
      })

      // Process each document
      for (const upload of newUploads) {
        const payload = JSON.parse(upload.payload)
        console.log(
          `⏳ Processing document: ${payload.fileName} (ID: ${payload.documentId})`
        )
        await processDocumentAsFileRequest({
          processId: upload.id,
          payload
        })
        processedDocuments++

        // wait 3 seconds between each document to avoid rate limiting
        await new Promise((resolve) => setTimeout(resolve, 3000))
      }
    }

    const totalTime = (Date.now() - startTime) / 1000
    // console.log(
    //   `✅ Document processing cron job completed successfully in ${totalTime.toFixed(2)}s`
    // )

    // Generate appropriate response message based on results
    let responseMessage = ''
    if (processedDocuments === 0) {
      responseMessage = 'No documents to process'
    } else {
      responseMessage = `Successfully processed ${processedDocuments} document(s) in ${totalTime.toFixed(2)} seconds`
    }

    res.status(200).json({
      message: responseMessage,
      processed: processedDocuments,
      time: totalTime
    })
  } catch (e: any) {
    console.error('❌ Document processing error:', e)
    res.status(500).json({ error: 'Failed to index.', errorcontent: e })
  }
}

async function processDocumentAsFileRequest({
  processId,
  payload
}: {
  processId: string
  payload: {
    documentId: number
    s3Key: string
    fileName: string
    fileType: string
    binderId?: string
    ref?: string
  }
}) {
  try {
    const { documentId, s3Key, fileName, fileType } = payload
    console.log(
      `🔍 Starting to process document: ${fileName} (ID: ${documentId})`
    )
    const startTime = Date.now()

    // Get S3 URL using the helper method
    console.log(`📁 Getting S3 URL for key: ${s3Key}`)
    const s3Url = getS3Url({
      key: s3Key,
      bucket: AWS_BUCKET.secure
    })

    // Download the file from S3 using the helper method
    // console.log('⬇️ Downloading file from S3')
    const buffer = await s3DownloadFile({
      key: s3Key,
      bucket: AWS_BUCKET.secure
    })
    // console.log('✅ File downloaded successfully')

    console.log(`🔍 Fetching document record for ID: ${documentId}`)
    const document = await db.documentRecords.findUnique({
      where: {
        id: documentId
      }
    })

    if (!document) {
      await db.queuedEventProcess.update({
        where: {
          id: processId
        },
        data: {
          status: QueuedEventStatus.failed,
          response: JSON.stringify({
            error: 'Document record not found'
          })
        }
      })
      return
    }

    // Extract content using the downloaded buffer
    // console.log('📄 Extracting content from document')
    const { htmlContent, textContent } =
      await extractFileContentAndHtmlWithAzure({
        file: {
          buffer,
          type: fileType,
          name: fileName
        }
      })
    // console.log('✅ Document content extracted successfully')

    // Create document resource record
    // console.log('💾 Creating document resource record')
    await db.documentResource.create({
      data: {
        documentRecordId: documentId,
        s3Key,
        fileUrl: s3Url
      }
    })

    // Process document for embeddings
    // console.log('🔪 Splitting document into chunks for embedding')
    const splitter = RecursiveCharacterTextSplitter.fromLanguage('markdown', {
      chunkSize: 1000,
      chunkOverlap: 200
    })

    const splitDocuments = await splitter.createDocuments([textContent])
    console.log(`📊 Document split into ${splitDocuments.length} chunks`)

    // Process document chunks in batches
    const batchSize = 20
    console.log(`🔢 Processing embeddings in batches of ${batchSize}`)
    for (let i = 0; i < splitDocuments.length; i += batchSize) {
      console.log(
        `🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(splitDocuments.length / batchSize)}`
      )
      const batch = splitDocuments.slice(i, i + batchSize)
      const promises = batch.map(async (doc) => {
        doc.metadata = {
          ...doc.metadata,
          documentRecordsId: document.id,
          documentRecordsSource: document.source,
          refId: document.ref
        }
        return storePrivateTextEmbedding({
          inputText: doc.pageContent,
          namespace: document.source,
          docId: document.id,
          metadata: doc.metadata
        })
      })
      await Promise.all(promises)
    }
    // console.log('✅ All embeddings stored successfully')

    // Update document record
    // console.log('📝 Updating document record with content and status')
    await db.documentRecords.update({
      where: {
        id: documentId
      },
      data: {
        html: htmlContent,
        content: textContent,
        url: s3Url,
        indexed: true
      }
    })

    const endTime = Date.now()
    const duration = endTime - startTime

    console.log(
      `✅ Document processing completed in ${duration / 1000} seconds`
    )
    await db.queuedEventProcess.update({
      where: {
        id: processId
      },
      data: {
        status: QueuedEventStatus.completed,
        response: JSON.stringify({
          duration: duration / 1000,
          chunks: splitDocuments.length
        })
      }
    })
  } catch (error) {
    console.error('❌ Error processing document:', error)
    await db.queuedEventProcess.update({
      where: {
        id: processId
      },
      data: {
        status: QueuedEventStatus.failed,
        response: JSON.stringify(error)
      }
    })
  }
}
