import { notFound, redirect } from 'next/navigation'

import { authOptions } from '@/lib/auth'
import { db } from '@/lib/db'
import { getCurrentUser } from '@/lib/session'
import Head from 'next/head'
import '@/styles/document-viewer.css'
import { CaseData } from '@/types/document'
import { TextHighlighter } from '../../../../components/elements/research/text-highlighter'
import { AWS_BUCKET, s3UrlGet } from '@/lib/services/s3-service'

interface EditorPageProps {
  params: { documentId: string }
}

const passableSources = ['executive_orders_trump_2']

export default async function EditorPage({ params }: EditorPageProps) {
  const document = await db.documentRecords.findFirst({
    where: {
      // source: {
      //   in: [
      //     'justicia',
      //     'labourlawreporter',
      //     'taxindiaonline',
      //     'taxindiaonline-income-tax',
      //     'new-criminal-law',
      //     'old-criminal-law',
      //     user.teamId
      //   ]
      // },
      id: parseInt(params.documentId)
    },
    select: {
      id: true,
      title: true,
      date: true,
      url: true,
      meta: true,
      source: true
    }
  })

  if (!document) {
    return notFound()
  }

  const user = await getCurrentUser()

  if (!user && !passableSources.includes(document.source)) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  if (user) {
    const isTeamDoc = await db.team.findUnique({
      where: {
        id: document.source
      }
    })

    if (isTeamDoc && user.teamId !== document.source) {
      return notFound()
    }
  }

  const metadata = document.meta
    ? (JSON.parse(document.meta) as CaseData)
    : null

  // Check if the document has a URL and if it's a protected S3 URL
  const isS3ProtectedUrl =
    document.url && document.url.includes(`${AWS_BUCKET.secure}.s3.`)
  let documentUrl = document.url
  let isPdf = false
  let htmlbody = ''

  if (isS3ProtectedUrl) {
    try {
      // Extract the S3 key from the URL
      const urlParts = new URL(document.url)
      const path = urlParts.pathname.startsWith('/')
        ? urlParts.pathname.substring(1)
        : urlParts.pathname

      // Get a presigned URL for the protected document
      documentUrl = await s3UrlGet({
        key: path,
        bucket: AWS_BUCKET.secure,
        expiresIn: 3600 // 1 hour expiration
      })

      isPdf = true
    } catch (error) {
      console.error('Error generating presigned URL:', error)
      // Fall back to HTML rendering if URL processing fails
      documentUrl = ''
    }
  } else {
    const docHtml = await db.documentRecords.findFirst({
      where: {
        id: document.id
      },
      select: {
        html: true
      }
    })

    if (!isPdf && docHtml!.html) {
      htmlbody = docHtml!.html
      if (htmlbody.split('<body class="mid1">')[1]) {
        htmlbody = htmlbody.split('<body class="mid1">')[1].split('</body>')[0]
      }

      htmlbody = htmlbody.replace(/class="pdf-iframe"/g, 'class="hidden"')
      htmlbody = htmlbody.replace(/class="disclaimer"/g, 'class="hidden"')
    }
  }

  return (
    <>
      <Head>
        <title>{document.title}</title>
        <meta
          name="description"
          content={`Case of ${document.title} dated ${document.date}`}
        />
        <meta
          property="og:description"
          content={`Case of ${document.title} dated ${document.date}`}
        />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="robots" content="noindex,nofollow" />
      </Head>
      <div className="flex flex-col md:flex-row gap-2 justify-between sticky top-0 z-10 p-3 bg-background border-b-2">
        <div className="flex flex-col md:flex-row gap-2 items-end">
          <h1 className="text-xl md:text-3xl font-bold">{document.title}</h1>{' '}
          <span className="text-sm text-gray-500">(alpha)</span>
          <span className="text-sm text-transparent">{document.id}</span>
        </div>
        <div className="flex gap-5 items-end">
          <TextHighlighter />
        </div>
      </div>

      {isPdf && documentUrl ? (
        <div className="h-[calc(100vh-100px)] w-full overflow-auto">
          <iframe
            src={`${documentUrl}#view=FitH`}
            className="w-full h-full border-0"
            title={document.title}
            allowFullScreen
          />
        </div>
      ) : (
        <div
          dangerouslySetInnerHTML={{ __html: htmlbody }}
          className="text-black xdark:text-white"
        />
      )}
    </>
  )
}
