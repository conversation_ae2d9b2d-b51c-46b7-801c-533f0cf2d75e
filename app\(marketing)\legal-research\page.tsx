import Link from 'next/link'

import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import Image from 'next/image'
import { Icons } from '@/components/elements/icons'

export default async function IndexPage() {
  const features = [
    {
      title: 'Legal Research',
      description: 'Delve into tailored legal insights for your jurisdiction.',
      icon: 'research'
    },
    {
      title: 'Deposition Preparation',
      description: 'Engage with AI for insightful deposition preparation.',
      icon: 'converse'
    },
    {
      title: 'Document Upload',
      description: 'Upload and review documents to extract critical info.',
      icon: 'document-upload'
    },
    {
      title: 'Document Comparison',
      description: 'Compare your documents with existing legal data.',
      icon: 'document-compare'
    },
    {
      title: 'Contract Revision',
      description: 'Ensure contract compliance with specified policies.',
      icon: 'contract-revision'
    }
    // {
    //   title: 'Search Database',
    //   description: 'Search our database for relevant legal documents.',
    //   icon: 'search-database'
    // }
  ]

  return (
    <>
      <section className="space-y-6 pb-8 pt-6 md:pb-12 md:pt-10 lg:py-32">
        <div className="container flex max-w-[64rem] flex-col items-center gap-4 text-center">
          <Icons.logoWide className="xs:block lg:hidden" />
          <h1 className="font-heading text-3xl sm:text-5xl md:text-6xl lg:text-7xl">
            Transform Your Legal Workflow with AI-Powered Precision
          </h1>
          <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
            Experience Seamless, Comprehensive, and Accurate Legal Research,
            Document Analysis, Drafting, and Knowledge Management -<br />
            by our secure, all-in-one platform that
            <br />
            guarantees complete confidentiality and data privacy.
          </p>
          <div className="space-x-4">
            <Link
              href="/register"
              className={cn(buttonVariants({ size: 'lg' }))}
            >
              Get Started
            </Link>
          </div>
        </div>
      </section>
      <section
        id="features"
        className="container space-y-16 bg-slate-50 py-8 xdark:bg-transparent md:py-12 lg:py-18"
      >
        <div className="mx-auto max-w-[58rem] flex flex-col items-center space-y-4 text-center">
          <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-5xl">
            Supported By
          </h2>
        </div>
        <div className="mx-auto grid justify-center gap-4 sm:grid-cols-1 md:max-w-[64rem] md:grid-cols-3">
          {SupportedBy.map((support, index) => (
            <Image
              key={index}
              src={support.logo}
              alt={support.title}
              className={cn(
                'm-auto h-14 w-auto',
                support.invert &&
                  'xdark:invert xdark:saturate-100 xdark:brightness-0'
              )}
              width={300}
              height={300}
            />
          ))}
        </div>
      </section>
      <section
        id="features"
        className="container space-y-6 bg-slate-50 py-8 xdark:bg-transparent md:py-12 lg:py-24"
      >
        <div className="mx-auto max-w-[58rem] flex flex-col items-center space-y-4 text-center">
          <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
            Features
          </h2>
          <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
            Uncover a range of meticulously crafted legal tools tailored to meet
            the diverse needs of modern legal practices. Experience a blend of
            precision and efficiency as you navigate through legal research,
            document review, contract revision, and database search, all under
            one roof.
          </p>
        </div>
        <div className="mx-auto grid justify-center gap-4 sm:grid-cols-2 md:max-w-[64rem] md:grid-cols-3">
          {features.map((feature, index) => (
            <Feature
              key={index}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>
      </section>
    </>
  )
}

const Feature = ({
  title,
  description,
  Icon
}: {
  title: string
  description: string
  Icon?: React.ComponentType
}) => {
  return (
    <div className="relative overflow-hidden rounded-lg border bg-background p-2">
      <div className="flex h-[180px] flex-col justify-between rounded-md p-6">
        {/* <Icon/> */}
        <div className="space-y-2">
          <h3 className="font-bold">{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
      </div>
    </div>
  )
}

const SupportedBy = [
  {
    title: 'AWS',
    invert: true,
    logo: '/marketing/logos/aws.png'
  },
  {
    title: 'Microsoft',
    invert: true,
    logo: '/marketing/logos/microsoft.png'
  },
  {
    title: 'OpenAI',
    invert: true,
    logo: '/marketing/logos/openaimono.png'
  }
]
