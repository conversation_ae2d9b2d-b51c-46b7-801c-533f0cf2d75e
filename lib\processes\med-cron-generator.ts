import { logger } from './utils-llm'
import { AuthUser } from 'next-auth'
import { DocumentContent, MedicalChronology } from '@/types/case'
import { MedicalChronologyStrategyFormData } from '@/components/elements/forms/medical-chronology-strategy-form'

// Import all services
import { extractDataFromDocuments } from './medcron-generator/event-extraction.service'
import { deduplicateEventsWithBatching } from './medcron-generator/deduplication.service'
import {
  structureMetadata,
  processUserStrategyInputs
} from './medcron-generator/metadata.service'
import {
  organizeChronologicalEventsFromDB,
  linkSourceDocuments
} from './medcron-generator/chronology.service'
import { identifyLegalGaps } from './medcron-generator/gap-analysis.service'
import { generateMarkdownReport } from './medcron-generator/report-generation.service'
import {
  checkExistingEventsStatus,
  structureExtractedDataFromEvents,
  filterDocumentsToProcess,
  mergeExtractedData,
  fetchDeduplicatedEvents,
  cleanupExistingEvents,
  getEventRelevanceStats
} from './medcron-generator/database.service'
import { createErrorCollector } from './medcron-generator/utils'
import { MedicalChronologyOptions } from './medcron-generator/types'

/**
 * Main function to orchestrate the medical chronology generation process.
 *
 * This function follows an optimized approach:
 * - Only processes documents that don't already have events in the database
 * - Uses existing events directly when available
 * - Supports force regeneration to override existing events
 * - Minimizes redundant API calls and processing time
 *
 * @param documents - Record of document types and their contents
 * @param user - Authenticated user for API calls
 * @param strategyInputs - User-defined strategy guidelines for processing
 * @param binderId - Optional binder ID for database storage and event tracking
 * @param options - Optional configuration including forceRegenerate flag
 * @returns Promise<MedicalChronology> - Complete medical chronology report
 */
export async function generateMedicalChronology(
  documents: Record<string, DocumentContent[]>,
  user: AuthUser,
  strategyInputs: MedicalChronologyStrategyFormData,
  binderId?: string,
  options?: MedicalChronologyOptions
): Promise<MedicalChronology> {
  logger.start('generateMedicalChronology', {
    documentCount: Object.values(documents).flat().length
  })

  try {
    // Create error collector for deduplication
    const errorCollector = createErrorCollector()

    let extractedData: Record<string, any> = {}

    if (binderId) {
      // Handle force regeneration option
      if (options?.forceRegenerate) {
        logger.info(
          'Force regeneration requested, cleaning up all existing events'
        )
        await cleanupExistingEvents(binderId, true) // Force cleanup all events

        // Process all documents since we're regenerating everything
        extractedData = await extractDataFromDocuments(
          documents,
          user,
          strategyInputs,
          binderId
        )
      } else {
        // Step 1: Check existing events status and only process documents without events
        logger.info('Checking existing events status')
        const allDocumentIds = Object.values(documents)
          .flat()
          .map((doc) => doc.id)
        const eventsStatus = await checkExistingEventsStatus(
          binderId,
          allDocumentIds
        )

        logger.info(
          `📊 Events Status: ${eventsStatus.documentsWithEvents.length}/${allDocumentIds.length} documents have events`
        )
        logger.info(
          `📊 Total existing events: ${eventsStatus.totalExistingEvents} (${eventsStatus.totalProcessedEvents} processed)`
        )

        if (eventsStatus.documentsWithoutEvents.length === 0) {
          logger.info(
            '✅ All documents already have events stored, using existing events'
          )

          // Fetch existing events from database
          const existingEvents = await fetchDeduplicatedEvents(
            binderId,
            documents
          )

          // Structure extracted data with existing events
          extractedData = structureExtractedDataFromEvents(
            existingEvents,
            documents
          )
        } else {
          logger.info(
            `📄 Processing ${eventsStatus.documentsWithoutEvents.length} documents without existing events`
          )

          // Only process documents that don't have events
          const documentsToProcess = filterDocumentsToProcess(
            documents,
            eventsStatus.documentsWithoutEvents
          )

          // Extract data only from documents that need processing
          const newExtractedData = await extractDataFromDocuments(
            documentsToProcess,
            user,
            strategyInputs,
            binderId
          )

          // Fetch existing events and combine with new data
          const existingEvents = await fetchDeduplicatedEvents(
            binderId,
            documents
          )

          // Merge existing and new extracted data
          extractedData = mergeExtractedData(
            newExtractedData,
            existingEvents,
            documents
          )
        }
      }
    } else {
      // No binderId provided, process all documents normally
      logger.info('No binderId provided, processing all documents')
      extractedData = await extractDataFromDocuments(
        documents,
        user,
        strategyInputs,
        binderId
      )
    }

    // Step 1.5: Deduplicate events if binderId is provided and we processed new documents
    if (binderId) {
      logger.info('Starting event deduplication process with batch processing')
      await deduplicateEventsWithBatching(
        binderId,
        errorCollector,
        strategyInputs,
        user
      )

      // Always fetch the latest deduplicated events from database after deduplication
      logger.info('Fetching latest deduplicated events from database')
      const deduplicatedEvents = await fetchDeduplicatedEvents(
        binderId,
        documents
      )

      logger.info(
        `📊 Total deduplicated events loaded: ${deduplicatedEvents.length}`
      )

      // Get relevance score statistics
      await getEventRelevanceStats(binderId)

      // Update extractedData with latest deduplicated events from database
      Object.keys(extractedData).forEach((docType) => {
        extractedData[docType] = extractedData[docType].map((doc: any) => {
          const docEvents = deduplicatedEvents.filter(
            (event: any) => event.sourceDocumentId === doc.sourceDocumentId
          )
          return {
            ...doc,
            events: docEvents
          }
        })
      })

      // Log final event distribution
      const totalFinalEvents = Object.values(extractedData)
        .flat()
        .reduce((sum: number, doc: any) => sum + (doc.events?.length || 0), 0)
      logger.info(
        `📊 Final event distribution: ${totalFinalEvents} events across ${Object.keys(extractedData).length} document types`
      )
    }

    // Step 2: Structure metadata fields with filtered events
    logger.info('Starting metadata processing with filtered events')
    const metadataResult = await structureMetadata(extractedData, user)

    // Step 3: Organize events chronologically using database sorting
    logger.info('Organizing events chronologically from database')
    const events = await organizeChronologicalEventsFromDB(
      extractedData,
      binderId
    )

    // Process user strategy inputs if provided
    logger.info('Processing user strategy inputs')
    const processedStrategy = strategyInputs
      ? await processUserStrategyInputs(strategyInputs, user)
      : null

    const { plaintiffInfo, incidentDetails } = metadataResult

    // Step 4: Link source documents - This is quick and doesn't need an API call
    logger.info('Linking source documents')
    const sourceLinks = await linkSourceDocuments(events, documents)

    // Step 5: Identify legal gaps and case strength
    logger.info(
      'Identifying legal gaps and case strength based on extracted data'
    )
    const caseGaps = await identifyLegalGaps(
      events,
      plaintiffInfo,
      incidentDetails,
      documents,
      user
    )

    logger.info('Generating final markdown report')

    // Step 6: Generate the final markdown report
    const markdownReport = await generateMarkdownReport(
      {
        plaintiffInfo,
        incidentDetails,
        events,
        sourceLinks,
        caseGaps
      },
      user,
      processedStrategy
    )

    const result = {
      plaintiffInfo,
      incidentDetails,
      events,
      sourceLinks,
      caseGaps,
      markdownReport,
      strategyInputs: strategyInputs ?? undefined
    }

    logger.end('generateMedicalChronology')
    return result
  } catch (error: any) {
    logger.error('generateMedicalChronology', error)
    throw new Error(`Failed to generate medical chronology: ${error.message}`)
  }
}
