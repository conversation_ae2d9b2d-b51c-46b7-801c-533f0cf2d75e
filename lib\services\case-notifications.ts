import { sendUserActivityNotification } from './slack-service'
import { sendEmailViaPostmark } from './postmark-service'
import { medicalChronologyCompletedEmailTemplate } from '../templates/email/med-cron'
import { AuthUser } from 'next-auth'

export interface CaseEventData {
  eventType: 'med-cron' | 'case-eval' | 'demand-letter'
  binderId: string
  userId: string
  userName: string
  userEmail: string
  teamId?: string
  processId?: string
  queueId?: string
  documentCount?: number
  timestamp?: Date
  error?: string
  duration?: number
}

/**
 * Send notification when a case process is initiated
 */
export async function notifyCaseProcessInitiated(data: CaseEventData) {
  const { eventType, binderId, userName, userEmail, queueId, documentCount } =
    data

  const eventDisplayName = {
    'med-cron': 'Medical Chronology',
    'case-eval': 'Case Evaluation',
    'demand-letter': 'Demand Letter'
  }[eventType]

  const message = `🚀 *${eventDisplayName} Generation Initiated*
📁 Binder ID: \`${binderId}\`
👤 User: ${userName} (${userEmail})
📄 Documents: ${documentCount || 'N/A'} selected
🔗 Queue ID: \`${queueId}\`
⏰ Time: ${new Date().toISOString()}`

  await sendUserActivityNotification(message)
}

/**
 * Send notification when cron job starts processing
 */
export async function notifyCaseProcessStarted(data: CaseEventData) {
  const { eventType, binderId, processId, userName } = data

  const eventDisplayName = {
    'med-cron': 'Medical Chronology',
    'case-eval': 'Case Evaluation',
    'demand-letter': 'Demand Letter'
  }[eventType]

  const message = `⚡ *${eventDisplayName} Processing Started*
📁 Binder ID: \`${binderId}\`
🔄 Process ID: \`${processId}\`
👤 User: ${userName}
⏰ Started: ${new Date().toISOString()}`

  await sendUserActivityNotification(message)
}

/**
 * Send notification when case process completes successfully
 */
export async function notifyCaseProcessCompleted(data: CaseEventData) {
  const { eventType, binderId, processId, userName, userEmail, duration } = data

  const eventDisplayName = {
    'med-cron': 'Medical Chronology',
    'case-eval': 'Case Evaluation',
    'demand-letter': 'Demand Letter'
  }[eventType]

  const durationText = duration
    ? `⏱️ Duration: ${Math.round(duration / 1000)}s`
    : ''

  const message = `✅ *${eventDisplayName} Generation Completed*
📁 Binder ID: \`${binderId}\`
🔄 Process ID: \`${processId}\`
👤 User: ${userName}
${durationText}
⏰ Completed: ${new Date().toISOString()}`

  // Send Slack notification
  await sendUserActivityNotification(message)

  // Send email notification for medical chronology completions
  if (eventType === 'med-cron' && userEmail) {
    try {
      const htmlTemplate = medicalChronologyCompletedEmailTemplate({
        userName,
        binderId
      })

      await sendEmailViaPostmark({
        to: userEmail,
        subject: 'Medical Chronology Complete - Ready for Review',
        htmlBody: htmlTemplate,
        tag: 'medical-chronology-completed'
      })

      console.log(`Medical chronology completion email sent to ${userEmail}`)
    } catch (error) {
      console.error(
        'Failed to send medical chronology completion email:',
        error
      )
    }
  }
}

/**
 * Send notification when case process fails
 */
export async function notifyCaseProcessFailed(data: CaseEventData) {
  const { eventType, binderId, processId, userName, error, duration } = data

  const eventDisplayName = {
    'med-cron': 'Medical Chronology',
    'case-eval': 'Case Evaluation',
    'demand-letter': 'Demand Letter'
  }[eventType]

  const durationText = duration
    ? `⏱️ Duration: ${Math.round(duration / 1000)}s`
    : ''
  const errorText = error
    ? `❌ Error: ${error.substring(0, 200)}${error.length > 200 ? '...' : ''}`
    : ''

  const message = `🚨 *${eventDisplayName} Generation Failed*
📁 Binder ID: \`${binderId}\`
🔄 Process ID: \`${processId}\`
👤 User: ${userName}
${durationText}
${errorText}
⏰ Failed: ${new Date().toISOString()}`

  await sendUserActivityNotification(message)
}

/**
 * Helper function to extract user data for notifications
 */
export function createCaseEventData(
  eventType: CaseEventData['eventType'],
  binderId: string,
  user: AuthUser,
  additionalData: Partial<CaseEventData> = {}
): CaseEventData {
  return {
    eventType,
    binderId,
    userId: user.id,
    userName: user.name || 'Unknown',
    userEmail: user.email || 'Unknown',
    teamId: user.teamId,
    timestamp: new Date(),
    ...additionalData
  }
}
