import { MainNav } from '@/components/elements/layout/main-nav'
import { SiteFooter } from '@/components/elements/layout/site-footer'
import { dashboardConfig } from '@/config/dashboard'

interface EditorProps {
  children?: React.ReactNode
}

export default function DocumentView({ children }: EditorProps) {
  return (
    <div className="container mx-auto grid content-between gap-10 py-8 min-h-screen">
      <header className="sticky top-0 z-40 border-b bg-background">
        <div className="container flex h-16 items-center justify-between py-4">
          <MainNav items={dashboardConfig.mainNav} />
        </div>
      </header>
      {children}

      <SiteFooter className="border-t bg-[#1c2519]" />
    </div>
  )
}
