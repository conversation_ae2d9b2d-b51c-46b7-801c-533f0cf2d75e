'use client'

import { useRouter } from 'next/navigation'
import EditorJS from '@editorjs/editorjs'
import { zodResolver } from '@hookform/resolvers/zod'
import { ShowCauseNotice } from '@prisma/client'
import { useForm } from 'react-hook-form'
import TextareaAutosize from 'react-textarea-autosize'
import * as z from 'zod'

import '@/styles/editor.css'
import { cn } from '@/lib/utils'
import { copyStringToClipboard } from '@/lib/utils-client'
import { buttonVariants } from '@/components/ui/button'
import { toast } from '@/components/ui/use-toast'
import { Icons } from '@/components/elements/icons'
import { ShellGeneratorSkeleton } from './layout/shell-generator-skeleton'
import { useRef, useState, useCallback, useEffect } from 'react'
import { showcauseOrderPatchSchema } from '@/lib/validations/showcause-order'
import { Progress } from '../ui/progress'

interface EditorProps {
  showCauseNoticeOrder: Pick<
    ShowCauseNotice,
    | 'id'
    | 'title'
    | 'orderLength'
    | 'processedLength'
    | 'finalJudgementSummary'
    | 'status'
  >
}

type FormData = z.infer<typeof showcauseOrderPatchSchema>

export function ShowCauseOrderEditor({ showCauseNoticeOrder }: EditorProps) {
  const { setValue, register, watch, handleSubmit } = useForm<FormData>({
    resolver: zodResolver(showcauseOrderPatchSchema)
  })
  const ref = useRef<EditorJS>()

  const body = showcauseOrderPatchSchema.parse(showCauseNoticeOrder)
  const finalJudgementSummaryData = body.finalJudgementSummary
    ? JSON.parse(body.finalJudgementSummary)
    : undefined

  const router = useRouter()
  const [isProcessing, setIsProcessing] = useState<boolean>(true)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const [isMounted, setIsMounted] = useState<boolean>(false)
  const [progress, setProgress] = useState<number>(5)
  const title = watch('title', showCauseNoticeOrder.title)

  const initializeEditor = useCallback(async () => {
    const EditorJS = (await import('@editorjs/editorjs')).default
    const Header = (await import('@editorjs/header')).default
    const Embed = (await import('@editorjs/embed')).default
    const Table = (await import('@editorjs/table')).default
    const List = (await import('@editorjs/list')).default
    const Code = (await import('@editorjs/code')).default
    const LinkTool = (await import('@editorjs/link')).default
    const InlineCode = (await import('@editorjs/inline-code')).default

    if (!ref.current) {
      const editor = new EditorJS({
        holder: 'editor',
        onReady() {
          ref.current = editor
        },
        placeholder: 'Type here to write your final order...',
        inlineToolbar: true,
        data: finalJudgementSummaryData,
        tools: {
          header: Header,
          linkTool: LinkTool,
          list: List,
          code: Code,
          inlineCode: InlineCode,
          table: Table,
          embed: Embed
        }
      })
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [showCauseNoticeOrder, finalJudgementSummaryData])

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsMounted(true)
    }
  }, [])

  useEffect(() => {
    if (isMounted && !isProcessing) {
      initializeEditor()

      return () => {
        ref.current?.destroy()
        ref.current = undefined
      }
    }
  }, [isMounted, isProcessing, initializeEditor])

  useEffect(() => {
    async function checkProcessingStatus() {
      const response = await fetch(`/api/show-cause/${showCauseNoticeOrder.id}`)
      const resData = await response.json()
      console.log(
        `Processor state: ${resData.processedLength} / ${resData.orderLength}`
      )
      setProgress((resData.processedLength / resData.orderLength) * 100 || 15)
      if (resData.processedLength >= resData.orderLength) {
        setIsProcessing(false)
      } else {
        setIsProcessing(true)
      }
      if (resData.refresh) {
        checkProcessingStatus()
      }
    }
    checkProcessingStatus()
  }, [showCauseNoticeOrder.id])

  async function onSubmit(data: FormData) {
    setIsSaving(true)

    const blocks = await ref.current?.save()

    const response = await fetch(`/api/show-cause/${showCauseNoticeOrder.id}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        title: data.title,
        content: blocks
      })
    })

    setIsSaving(false)

    if (!response?.ok) {
      return toast({
        title: 'Something went wrong.',
        description: 'Your order was not saved. Please try again.',
        variant: 'destructive'
      })
    }

    router.refresh()

    return toast({
      description: 'Your order has been saved.'
    })
  }

  if (!isMounted) {
    return null
  }

  return (
    <section>
      {isProcessing && (
        <div className="absolute inset-0 bg-gray-100 opacity-75 flex flex-col gap-4 items-center justify-center z-10">
          <h2 className="text-4xl font-bold">Processing Show Cause Order...</h2>
          <Progress value={progress} className="w-1/2 shadow-sm" />
        </div>
      )}
      <div className="grid w-full gap-10">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center space-x-10">
            <p className="text-sm text-muted-foreground">
              {showCauseNoticeOrder.status}
            </p>
          </div>
          <div className="flex gap-3">
            <button
              className={cn(buttonVariants())}
              disabled={isProcessing}
              onClick={() =>
                copyStringToClipboard(
                  `${window.location.origin}/dashboard/show-cause-notice/${showCauseNoticeOrder.id}`
                )
              }
            >
              Copy Share URL
            </button>
            <button
              onClick={handleSubmit(onSubmit)}
              type="submit"
              disabled={isProcessing}
              className={cn(buttonVariants())}
            >
              {isSaving && (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              )}
              <span>Save</span>
            </button>
          </div>
        </div>

        <div className="prose prose-stone mx-auto xdark:prose-invert">
          <form onSubmit={handleSubmit(onSubmit)}>
            <TextareaAutosize
              autoFocus
              id="title"
              defaultValue={title}
              placeholder="Show Cause Order title"
              className="w-full resize-none appearance-none overflow-hidden bg-transparent text-5xl font-bold focus:outline-none"
              {...register('title')}
              disabled={isProcessing}
            />
            {isSaving && <ShellGeneratorSkeleton />}
            <div
              id="editor"
              style={{
                display: isSaving ? 'none' : 'block'
              }}
              className="min-h-[500px]"
            />
          </form>
        </div>
      </div>
    </section>
  )
}
