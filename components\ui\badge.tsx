import * as React from 'react'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/lib/utils'

const badgeVariants = cva(
  'inline-flex items-center rounded-full border border-amber-900 px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-slate-950 focus:ring-offset-2 xdark:border-slate-800 xdark:focus:ring-slate-300',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-slate-900 text-slate-50 hover:bg-slate-900/80 xdark:bg-slate-50 xdark:text-slate-900 xdark:hover:bg-slate-50/80',
        secondary:
          'border-transparent bg-amber-100 text-slate-900 hover:bg-amber-200 xdark:bg-slate-800 xdark:text-slate-50 xdark:hover:bg-slate-800/80',
        destructive:
          'border-transparent bg-red-500 text-slate-50 hover:bg-red-500/80 xdark:bg-red-900 xdark:text-slate-50 xdark:hover:bg-red-900/80',
        warning:
          'border-transparent bg-yellow-500 text-slate-50 hover:bg-yellow-500/80 xdark:bg-yellow-900 xdark:text-slate-50 xdark:hover:bg-yellow-900/80',
        outline: 'text-slate-950 xdark:text-slate-50'
      }
    },
    defaultVariants: {
      variant: 'default'
    }
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
