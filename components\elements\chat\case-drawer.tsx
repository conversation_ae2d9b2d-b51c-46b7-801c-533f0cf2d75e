import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>er,
  She<PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger
} from '@/components/ui/sheet'
import { generateCaseTitle } from '@/lib/utils'
import { RESEARCH_QUERY_TYPE } from '@/types'
import type { SupabaseExtendedMetadata } from '@/types/document'

export function CaseDrawer({
  source,
  i,
  sheetSide,
  updateSource
}: {
  source: {
    docId: string
    refId: string
    title: string
    year: number
    data: SupabaseExtendedMetadata[]
  }
  i: number
  sheetSide: 'right' | 'bottom'
  updateSource?: (
    source: string,
    eventQuery: RESEARCH_QUERY_TYPE | null
  ) => Promise<void>
}) {
  const caseTitle = generateCaseTitle({
    metadata: source.data[0],
    title: source.title,
    year: source.year,
    i
  })

  function storeLinesInLocalStorage() {
    localStorage.setItem(
      'emphasis-lines',
      JSON.stringify(
        source.data.map((sourceContent) => sourceContent.pageContent)
      )
    )
  }

  return (
    <Sheet>
      <SheetTrigger
        onClick={storeLinesInLocalStorage}
        className="w-full"
        x-data={source.docId}
      >
        <div className="flex justify-between gap-2">
          <div className="mt-2 text-xs text-left border-2 py-2  flex items-center px-5 hover:bg-ring hover:xdark:bg-slate-700 cursor-pointer duration-200 rounded-lg w-full">
            <p className="!font-bold">
              {caseTitle} - {source.year}
            </p>
            {/* {source.data.map((sourceContent, idx) => (
              <div key={idx} className="my-3">
                - &quot;
                {sourceContent.pageContent
                  .slice(0, 300)
                  .replace(/[\n\r]/g, ' ')
                  .replace(/\s+/g, ' ')
                  .trim() + '...'}
                &quot;
                <p>
                  {sourceContent.metadata?.loc?.lines &&
                    `(Lines ${sourceContent.metadata?.loc?.lines?.from} to ${sourceContent.metadata?.loc?.lines?.to})`}
                </p>
              </div>
            ))} */}
          </div>
          {updateSource && (
            <>
              <div
                onClick={(event) => {
                  event.stopPropagation()
                  updateSource(source.docId, RESEARCH_QUERY_TYPE.SUMMARISE)
                }}
                className="mt-2 text-xs text-center border-2 px-5 hover:bg-ring hover:xdark:bg-slate-700 cursor-pointer duration-200 rounded-lg flex items-center"
              >
                Summarize
              </div>
              <div
                onClick={(event) => {
                  event.stopPropagation()
                  updateSource(source.docId, null)
                }}
                className="mt-2 text-xs text-center border-2 px-5 hover:bg-ring hover:xdark:bg-slate-700 cursor-pointer duration-200 rounded-lg flex items-center"
              >
                Analyze
              </div>
            </>
          )}
        </div>
      </SheetTrigger>
      <SheetContent
        className="md:max-w-[80%] md:w-[80%] bg-background xdark:bg-background"
        side={sheetSide}
      >
        <SheetHeader>
          <SheetTitle className="hidden">{caseTitle}</SheetTitle>
          <SheetDescription>
            <iframe
              src={`/document-viewer/${source.docId}`}
              className="w-full h-[90vh]"
            ></iframe>
          </SheetDescription>
        </SheetHeader>
      </SheetContent>
    </Sheet>
  )
}
