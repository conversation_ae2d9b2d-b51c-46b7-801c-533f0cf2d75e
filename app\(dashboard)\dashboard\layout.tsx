// app/dashboard/layout.tsx
import { redirect } from 'next/navigation'
import { dashboardConfig } from '@/config/dashboard'
import { getCurrentUser } from '@/lib/session'
import { MainNav } from '@/components/elements/layout/main-nav'
import { DashboardNav } from '@/components/elements/layout/nav'
import { SiteFooter } from '@/components/elements/layout/site-footer'
import { UserAccountNav } from '@/components/elements/user/user-account-nav'
import { cn } from '@/lib/utils'
import { buttonVariants } from '@/components/ui/button'
import Link from 'next/link'
import { fetchPeriodicCreditHistory } from '@/lib/recordstore-team'
import { ZohoSalesIQ } from '@/components/external/zoho-salesiq'
import CalendlyWidget from '@/components/external/calendly-widget'

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default async function DashboardLayout({
  children
}: DashboardLayoutProps) {
  const user = await getCurrentUser()

  if (!user) {
    redirect('/login')
  }

  let expirationDate = new Date()
  let daysLeft = 1

  if (user.plan === 'trial') {
    const stats = await fetchPeriodicCreditHistory({
      teamId: user.teamId,
      type: 'research'
    })
    if (stats) {
      stats.forEach((stat) => {
        if (stat.expiresAt > expirationDate) {
          expirationDate = stat.expiresAt
        }
      })

      const diff = expirationDate.getTime() - new Date().getTime()
      daysLeft = Math.ceil(diff / (1000 * 60 * 60 * 24))
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <header className="sticky top-0 z-40 bg-[#1c2519]">
        <div className="container flex h-16 items-center justify-between py-4">
          <MainNav
            items={dashboardConfig.mainNav}
            home={dashboardConfig.rootPath}
          />

          <div className="flex items-center gap-2">
            {user.plan === 'trial' && (
              <div className="flex items-center gap-5 mx-3">
                <div className="text-sm text-amber-400 hidden sm:block">
                  Trial ends in {daysLeft} days
                </div>

                <Link
                  className={cn(
                    buttonVariants({
                      variant: 'secondary',
                      size: 'sm'
                    })
                  )}
                  href="/dashboard/settings"
                >
                  Upgrade
                </Link>
              </div>
            )}
            <UserAccountNav user={user} />
          </div>
        </div>
      </header>

      {/* Trial banner - full width */}
      {user.plan === 'trial' && (
        <div className="w-full h-12 bg-[#1c2519] flex items-center justify-center gap-6 px-4">
          <span className="text-md font-semibold text-background">
            Book a demo to get the most out of SmartCounsel AI
          </span>
          <CalendlyWidget calendarId="prasanna-smartcounsel/30min" />
        </div>
      )}

      <div className="flex flex-1">
        <DashboardNav items={dashboardConfig.sidebarNav} />
        <main className="flex-1 overflow-auto">
          <div className="container py-6">{children}</div>
        </main>
      </div>

      <SiteFooter className="border-t bg-[#1c2519]" />
      <ZohoSalesIQ />
    </div>
  )
}
