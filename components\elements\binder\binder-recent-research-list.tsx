'use client'

import { cn, formatDate } from '@/lib/utils'
import { ResearchStore, ResearchType } from '@prisma/client'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger
} from '@/components/ui/sheet'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useState } from 'react'
import Link from 'next/link'
import { buttonVariants } from '@/components/ui/button'
import { BinderResearch } from './binder-research'

export function BinderRecentResearchList({
  researchHistory,
  path,
  researchType
}: {
  researchHistory: Pick<ResearchStore, 'id' | 'createdAt' | 'question'>[]
  path: string
  researchType: ResearchType
}) {
  const [limit, setLimit] = useState(5)

  return (
    <div>
      <div className="divide-y divide-border rounded-md border bg-background xdark:bg-slate-950 overflow-hidden">
        {researchHistory.slice(0, limit).map((research, index) => {
          const question = research.question
          const questionPreview =
            question && question.length > 150
              ? question.slice(0, 150) + '...'
              : question || 'Unused research'

          return (
            <Sheet key={index}>
              <SheetTrigger className="w-full">
                <div className="grid gap-1 text-left px-4 py-2">
                  <p className="font-semibold">{questionPreview}</p>
                  <div>
                    <p className="text-sm">
                      {formatDate(research.createdAt?.toDateString())}
                    </p>
                  </div>
                </div>
              </SheetTrigger>
              <SheetContent className="md:max-w-[80%] md:w-[80%] bg-background xdark:bg-background">
                <SheetHeader className="mt-4">
                  <SheetTitle>{question}</SheetTitle>
                  <Link
                    href={`${path}/${research.id}`}
                    className={cn(buttonVariants({ size: 'xs' }), 'w-fit')}
                  >
                    View Research
                  </Link>
                  <SheetDescription>
                    <ScrollArea className="h-[80dvh] rounded">
                      <BinderResearch
                        researchId={research.id}
                        researchType={researchType}
                      />
                    </ScrollArea>
                  </SheetDescription>
                </SheetHeader>
              </SheetContent>
            </Sheet>
          )
        })}
      </div>

      {researchHistory.length > limit && (
        <button
          onClick={() => setLimit(limit + 5)}
          className="text-amber-700 font-semibold text-sm mt-4"
        >
          Load More
        </button>
      )}
    </div>
  )
}
