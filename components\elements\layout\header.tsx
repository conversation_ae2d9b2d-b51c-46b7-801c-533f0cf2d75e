import { BreadcrumbNav } from './breadcrumb-nav'
import { Badge } from '../../ui/badge'

interface DashboardHeaderProps {
  heading: string
  text?: string
  children?: React.ReactNode
  showBreadcrumbs?: boolean
  badge?: 'alpha' | 'beta' | 'new feature' | string
}

export function DashboardHeader({
  heading,
  text,
  children,
  showBreadcrumbs = true,
  badge
}: DashboardHeaderProps) {
  return (
    <div className="space-y-2">
      {showBreadcrumbs && <BreadcrumbNav currentPageTitle={heading} />}
      <div className="flex md:items-center md:justify-between px-2 gap-4 flex-col md:flex-row">
        <div className="grid gap-1">
          <div className="flex items-center gap-3">
            <h1 className="font-heading font-semibold text-3xl md:text-4xl tracking-wide">
              {heading}
            </h1>
            {badge && (
              <Badge
                variant={
                  badge === 'alpha'
                    ? 'destructive'
                    : badge === 'beta'
                      ? 'warning'
                      : 'secondary'
                }
                className="text-xs font-medium mt-3"
              >
                {badge}
              </Badge>
            )}
          </div>
          {text && <p className="text-lg text-amber-800">{text}</p>}
        </div>
        {children}
      </div>
    </div>
  )
}
