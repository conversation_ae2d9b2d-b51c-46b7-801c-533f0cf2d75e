import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: { promptId: string } }
) {
  try {
    const { promptId } = params

    if (!promptId) {
      return NextResponse.json(
        { error: 'Prompt ID is required' },
        { status: 400 }
      )
    }

    // Fetch the current prompt details
    const currentPrompt = await db.prompt.findUnique({
      where: { id: promptId },
      select: {
        id: true,
        source: true,
        role: true,
        prompt: true,
        expectedOutput: true,
        variables: true,
        version: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!currentPrompt) {
      return NextResponse.json({ error: 'Prompt not found' }, { status: 404 })
    }

    // Fetch the last 10 history records for this prompt
    const history = await db.promptsHistory.findMany({
      where: { refId: promptId },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        source: true,
        role: true,
        prompt: true,
        expectedOutput: true,
        variables: true,
        createdAt: true
      }
    })

    return NextResponse.json({
      current: currentPrompt,
      history: history
    })
  } catch (error) {
    console.error('Error fetching prompt history:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
