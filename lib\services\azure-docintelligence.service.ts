import DocumentIntelligence from '@azure-rest/ai-document-intelligence'
import { DefaultAzureCredential } from '@azure/identity'
import {
  getLongRunning<PERSON>oller,
  AnalyzeOperationOutput,
  isUnexpected
} from '@azure-rest/ai-document-intelligence'
import { marked } from 'marked'

/**
 * Extract file content and HTML using Azure Document Intelligence API
 * This function matches the interface of extractFileContentAndHtmlWithLlamaParse
 *
 * @param file Object containing buffer, type, and name
 * @returns Object with textContent and htmlContent
 */
export async function extractFileContentAndHtmlWithAzure({
  file
}: {
  file: {
    buffer: Buffer
    type: string
    name: string
  }
}) {
  console.log('Extracting content from file with Azure:', file.name)

  // Check environment variables
  const endpoint = process.env.DOCUMENT_INTELLIGENCE_ENDPOINT
  const apiKey = process.env.DOCUMENT_INTELLIGENCE_API_KEY

  if (!endpoint) {
    console.error(
      'DOCUMENT_INTELLIGENCE_ENDPOINT environment variable is not set'
    )
    return {
      textContent: '',
      htmlContent: ''
    }
  }

  try {
    // Initialize client
    const client = apiKey
      ? DocumentIntelligence(endpoint, { key: apiKey })
      : DocumentIntelligence(endpoint, new DefaultAzureCredential())

    // Convert buffer to base64
    const base64Source = file.buffer.toString('base64')

    // Start analysis with markdown output
    console.log(
      'Starting document analysis with Azure Document Intelligence...'
    )
    const initialResponse = await client
      .path('/documentModels/{modelId}:analyze', 'prebuilt-layout')
      .post({
        contentType: 'application/json',
        body: {
          base64Source
        },
        queryParameters: {
          locale: 'en-US',
          outputContentFormat: 'markdown' // Request markdown format
        }
      })

    // Check for errors
    if (isUnexpected(initialResponse)) {
      console.error(
        'Azure Document Intelligence analysis failed:',
        initialResponse.body
      )
      return {
        textContent: '',
        htmlContent: ''
      }
    }

    console.log('Document analysis started, polling for results...')

    // Use built-in poller for long-running operation
    const poller = getLongRunningPoller(client, initialResponse)
    const result = (await poller.pollUntilDone()).body as AnalyzeOperationOutput

    // Check if analysis succeeded
    if (result.status !== 'succeeded') {
      console.error(`Analysis failed with status: ${result.status}`)
      return {
        textContent: '',
        htmlContent: ''
      }
    }

    // Extract the markdown content
    if (!result.analyzeResult?.content) {
      console.error('No content returned from the analysis')
      return {
        textContent: '',
        htmlContent: ''
      }
    }

    // Sanitize text content to remove invalid Unicode sequences (same as LlamaParse)
    let textContent = result.analyzeResult.content

    // Replace invalid surrogate pairs
    const invalidSurrogateRegex =
      '[\\ud800-\\udbff](?![\\udc00-\\udfff])|(?:[^\\ud800-\\udbff]|^)[\\udc00-\\udfff]'
    const re = new RegExp(invalidSurrogateRegex, 'g')
    textContent = textContent.replace(re, '')

    // Generate HTML safely (same as LlamaParse)
    let htmlContent = ''
    try {
      htmlContent = await marked(textContent)
    } catch (htmlError) {
      console.error('Error generating HTML:', htmlError)
      htmlContent = '<p>Error rendering document content</p>'
    }

    console.log('Document analysis complete')

    return { textContent, htmlContent }
  } catch (error) {
    console.error('Error parsing document with Azure:', error)
    return {
      textContent: '',
      htmlContent: ''
    }
  }
}
