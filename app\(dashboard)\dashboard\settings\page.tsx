import { getCurrentUser } from '@/lib/session'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { UserWelcomeForm } from '@/components/elements/forms/user-welcome-form'
import {
  fetchPeriodicCreditHistory,
  findTeamCreditStats
} from '@/lib/recordstore-team'
import { db } from '@/lib/db'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import Image from 'next/image'
import { IntegrateGDriveButton } from '@/components/elements/integrations/button-gdrive'
import QuestionAssessmentToggle from '@/components/elements/custom-components/question-assessment-toggle'
import { fetchUserQuestionAssessmentSettings } from '@/lib/actions/research'

export const metadata = {
  title: 'Settings',
  description: 'Manage account and website settings.'
}

export default async function SettingsPage() {
  const user = await getCurrentUser()

  const usage = await findTeamCreditStats({
    teamId: user!.teamId
  })

  const teamName = await db.team.findUnique({
    where: { id: user!.teamId },
    select: { name: true }
  })

  let expirationDate = new Date()
  let daysLeft = 1

  var questionAssessmentSetting: any = {}
  if (user && user.plan === 'trial') {
    const stats = await fetchPeriodicCreditHistory({
      teamId: user.teamId,
      type: 'research'
    })
    if (stats) {
      stats.forEach((stat) => {
        if (stat.expiresAt > expirationDate) {
          expirationDate = stat.expiresAt
        }
      })

      const diff = expirationDate.getTime() - new Date().getTime()
      daysLeft = Math.ceil(diff / (1000 * 60 * 60 * 24))
    }

    questionAssessmentSetting = await fetchUserQuestionAssessmentSettings(
      user.id
    )
  }

  // const documents = await db.documentRecords.findMany({
  //   select: {
  //     id: true,
  //     title: true,
  //     url: true,
  //     createdAt: true
  //   },
  //   where: {
  //     source: user!.teamId,
  //     region: user!.region
  //   },
  //   orderBy: {
  //     title: 'asc'
  //   }
  // })

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Settings"
        text="Manage account and subscription settings."
      />

      <Tabs defaultValue="account">
        <TabsList className="w-full mb-5 justify-start">
          <TabsTrigger value="account" className="w-1/3">
            Account
          </TabsTrigger>
          <TabsTrigger value="integration" className="w-1/3">
            Integrations
          </TabsTrigger>
          <TabsTrigger value="ai-functions" className="w-1/3">
            AI Functions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="account">
          <div className="grid gap-10 grid-cols-1 lg:grid-cols-2">
            <UserWelcomeForm
              user={user!}
              teamName={teamName?.name}
              creditUsageStats={usage}
            />

            <section className="flex flex-col gap-6">
              {user!.plan === 'trial' && (
                <>
                  <h3 className="font-heading text-xl leading-[1.1] sm:text-xl md:text-3xl">
                    You are on a free trial
                  </h3>
                  <div className="mx-auto flex flex-col gap-4">
                    <p className="max-w-[85%] leading-normal text-amber-800 xdark:text-amber-500 sm:leading-7">
                      Your trial ends in {daysLeft} days (
                      {expirationDate.toDateString()}). Upgrade to a paid plan
                      to continue using our services.
                    </p>
                    <p className="max-w-[85%] leading-normal text-amber-900 sm:leading-7">
                      For upgrading to a paid plan, please reach out to us over
                      at{' '}
                      <strong>
                        <a
                          href="mailto:<EMAIL>"
                          className="underline text-amber-900 xdark:text-blue-400"
                        >
                          <EMAIL>
                        </a>
                      </strong>
                    </p>
                    <p className="max-w-[85%] leading-normal text-amber-900 sm:leading-7">
                      <strong>
                        Our AI models extend from OpenAI and are thoroughly
                        reviewed by legal experts. If you come across any
                        disparities in our results, please reach out to us.
                      </strong>
                    </p>
                  </div>
                </>
              )}
            </section>
          </div>
        </TabsContent>

        <TabsContent value="integration">
          <ul className="max-w-md divide-y divide-gray-200 xdark:divide-gray-700 space-x-3">
            <li className="bg-gray-100 xdark:bg-gray-800 px-5 py-2 border rounded-lg">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <div className="flex-shrink-0">
                  <Image
                    className="w-8 h-8"
                    src="/props/icons/gdrive.png"
                    alt="Google Drive"
                    width={100}
                    height={100}
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">Google Drive</p>
                </div>
                <IntegrateGDriveButton />
              </div>
            </li>
          </ul>
        </TabsContent>
        <TabsContent value="ai-functions">
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <QuestionAssessmentToggle
                  userId={user?.id || ''}
                  questionAssessmentSetting={questionAssessmentSetting ?? true}
                />
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </DashboardShell>
  )
}
