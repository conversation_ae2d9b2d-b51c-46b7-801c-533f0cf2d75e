'use server'

import { CaseFileType } from '@prisma/client'
import { db } from '../../db'
import { UnauthorizedError } from '../../exceptions'
import { getCurrentUser } from '../../session'
import { generateDemandLetter } from '@/lib/processes/demand-letter-generator'
import {
  notifyCaseProcessInitiated,
  notifyCaseProcessCompleted,
  notifyCaseProcessFailed,
  createCaseEventData
} from '../../services/case-notifications'

export async function handleDemandLetterGeneration(binderId: string) {
  const startTime = Date.now()
  let user: any = null
  let caseFileId = ''

  try {
    user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Send Slack notification for process initiation
    await notifyCaseProcessInitiated(
      createCaseEventData('demand-letter', binderId, user, {})
    )

    const demandLetter = await generateDemandLetter(binderId)

    const caseFile = await db.caseFile.upsert({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.DEMAND_LETTER
        }
      },
      update: {
        content: demandLetter.combinedContent
      },
      create: {
        binderId,
        creatorId: user.id,
        fileType: CaseFileType.DEMAND_LETTER,
        content: demandLetter.combinedContent
      }
    })

    caseFileId = caseFile.id

    await db.teamCreditUsed.create({
      data: {
        teamId: user.teamId,
        type: 'case',
        refId: caseFile.id,
        eventId: new Date().getTime().toString()
      }
    })

    // Send success notification
    const duration = Date.now() - startTime
    await notifyCaseProcessCompleted(
      createCaseEventData('demand-letter', binderId, user, {
        processId: caseFileId,
        duration
      })
    )

    return demandLetter
  } catch (error) {
    console.error('Error in handleDemandLetterGeneration:', error)

    // Send failure notification
    try {
      if (user) {
        const duration = Date.now() - startTime
        await notifyCaseProcessFailed(
          createCaseEventData('demand-letter', binderId, user, {
            processId: caseFileId || 'unknown',
            duration,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        )
      }
    } catch (notifError) {
      console.error('Failed to send failure notification:', notifError)
    }

    throw error
  }
}

export async function updateDemandLetter(binderId: string, content: string) {
  try {
    const user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    const store = await db.caseFile.upsert({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.DEMAND_LETTER
        }
      },
      update: {
        content
      },
      create: {
        binderId,
        creatorId: user.id,
        fileType: CaseFileType.DEMAND_LETTER,
        content
      }
    })

    return store
  } catch (error) {
    console.error('Error in updateDemandLetter:', error)
    throw error
  }
}
