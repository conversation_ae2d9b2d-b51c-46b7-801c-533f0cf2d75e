import { db } from '../../db'
import { createGeminiCompletion } from '../../services/gemini-service'
import { logger } from '../utils-llm'
import {
  generateTreatmentCalendar,
  generateTimelineMarkdown
} from './report-utils.service'
import { MedicalChronologyBase } from '@/types/case'
import { GeminiModel } from '@/types'
import {
  convertChronologyEventsToDocumentEvents,
  convertChronologyEventsToTimelineEvents,
  estimateTokenCount
} from './utils'
import { MAX_EVENTS_FOR_REPORT, SAFE_TOKEN_LIMIT } from './constants'
import { AuthUser } from 'next-auth'

// Step 6: Generate the final markdown report
export async function generateMarkdownReport(
  chronology: MedicalChronologyBase,
  user: AuthUser,
  processedStrategy?: any | null
): Promise<string> {
  logger.start('generateMarkdownReport')
  const medChronPrompts = await db.prompt.findMany({
    where: {
      source: {
        in: [
          'AI_MEDICAL_CHRONOLOGY_CASE_INFORMATION_METADATA',
          'AI_MEDICAL_CHRONOLOGY_DIAGNOSTIC_HIGHLIGHTS',
          'AI_MEDICAL_CHRONOLOGY_TREATMENT_CALENDAR',
          'AI_MEDICAL_CHRONOLOGY_FLAGS_AND_CASE_GAPS',
          'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE',
          'AI_MEDICAL_CHRONOLOGY_CASE_STRENGTH_ANALYSIS'
        ]
      }
    }
  })

  const sortedPrompts = medChronPrompts.sort((a, b) => {
    const order = [
      'AI_MEDICAL_CHRONOLOGY_CASE_INFORMATION_METADATA',
      'AI_MEDICAL_CHRONOLOGY_DIAGNOSTIC_HIGHLIGHTS',
      'AI_MEDICAL_CHRONOLOGY_TREATMENT_CALENDAR',
      'AI_MEDICAL_CHRONOLOGY_FLAGS_AND_CASE_GAPS',
      'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE',
      'AI_MEDICAL_CHRONOLOGY_CASE_STRENGTH_ANALYSIS'
    ]
    return order.indexOf(a.source) - order.indexOf(b.source)
  })

  try {
    // Prepare optimized chronology data to reduce token usage
    const optimizedChronology = {
      plaintiffInfo: chronology.plaintiffInfo,
      incidentDetails: chronology.incidentDetails,
      eventsSummary: {
        totalEvents: chronology.events.length,
        eventsIncluded: Math.min(
          chronology.events.length,
          MAX_EVENTS_FOR_REPORT
        ),
        dateRange: {
          earliest: chronology.events[0]?.date || 'Unknown',
          latest:
            chronology.events[chronology.events.length - 1]?.date || 'Unknown'
        },
        eventBreakdown: chronology.events.reduce(
          (acc, event) => {
            acc[event.category] = (acc[event.category] || 0) + 1
            return acc
          },
          {} as Record<string, number>
        )
      },
      // Limit events for LLM sections
      events: chronology.events
        .slice(0, MAX_EVENTS_FOR_REPORT)
        .map((event) => ({
          ...event,
          // Truncate summaries to reduce tokens
          summary:
            event.summary.substring(0, 200) +
            (event.summary.length > 200 ? '...' : '')
        })),
      sourceLinks: chronology.sourceLinks.slice(0, 100), // Limit source links
      caseGaps: chronology.caseGaps
    }

    // Generate sections - some using imported utility functions, others using LLM
    const sectionResults: string[] = []

    for (const prompt of sortedPrompts) {
      try {
        let sectionContent = ''

        // Use imported utility functions for calendar and timeline sections
        if (prompt.source === 'AI_MEDICAL_CHRONOLOGY_TREATMENT_CALENDAR') {
          // Use ALL events for calendar generation (not sent to LLM)
          const documentEvents = convertChronologyEventsToDocumentEvents(
            chronology.events
          )
          sectionContent = generateTreatmentCalendar(documentEvents)
        } else if (
          prompt.source === 'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE'
        ) {
          // Use ALL events for timeline generation (not sent to LLM)
          const timelineEvents = convertChronologyEventsToTimelineEvents(
            chronology.events
          )
          sectionContent = generateTimelineMarkdown(timelineEvents)
        } else {
          // Use LLM for other sections with optimized data
          let sectionPrompt = prompt.prompt + prompt.expectedOutput
          let contextualPrompt = sectionPrompt.replace(
            '{{context}}',
            JSON.stringify(optimizedChronology)
          )

          // Special handling for treatment timeline section with strategy (not used anymore but kept for consistency)
          if (
            prompt.source === 'AI_MEDICAL_CHRONOLOGY_TREATMENT_TIMELINE' &&
            processedStrategy
          ) {
            contextualPrompt = contextualPrompt
              .replace(
                '{{partAGuidelines}}',
                JSON.stringify(processedStrategy.partAGuidelines || '')
              )
              .replace(
                '{{partBGuidelines}}',
                JSON.stringify(processedStrategy.partBGuidelines || '')
              )
          }

          const contextLength = contextualPrompt.length
          const estimatedTokens = estimateTokenCount(contextualPrompt)
          logger.info(
            `📊 Generating ${prompt.source} section (estimated ${estimatedTokens} tokens)`
          )

          // Check token limit
          if (estimatedTokens > SAFE_TOKEN_LIMIT) {
            logger.info(
              `⚠️ Token limit warning for ${prompt.source}: ${estimatedTokens} tokens. Skipping this section.`
            )
            sectionContent = `## ${prompt.source.replace('AI_MEDICAL_CHRONOLOGY_', '').replace(/_/g, ' ')}\n\n_Section skipped due to token limit constraints._\n`
            sectionResults.push(sectionContent)
            continue
          }

          // Include chronology data with the prompt
          const geminiResponse = await createGeminiCompletion({
            modelName: GeminiModel.Gemini25Pro,
            systemInstruction: contextualPrompt,
            message: 'Generate medical chronology report section',
            teamId: user.teamId,
            purpose: 'med-cron',
            activity: 'report-generation'
          })
          const responseContent =
            typeof geminiResponse === 'string'
              ? JSON.parse(geminiResponse)
              : geminiResponse

          for (const key of Object.keys(responseContent)) {
            sectionContent =
              typeof responseContent[key] === 'string'
                ? responseContent[key] + '\n\n'
                : JSON.stringify(responseContent[key], null, 2)
          }
        }

        sectionResults.push(sectionContent)
      } catch (error: any) {
        // Log error but don't fail the entire report
        logger.error(`generateMarkdownReport:${prompt.source}`, error)
        sectionResults.push('') // Add empty string for failed sections
      }
    }

    // Combine all sections in the correct order
    const combinedReport = sectionResults.join('\n\n')

    logger.end('generateMarkdownReport', {
      reportLength: combinedReport.length,
      sections: combinedReport.split('##').length - 1,
      eventsUsedInReport: optimizedChronology.events.length
    })

    return combinedReport
  } catch (error: any) {
    logger.error('generateMarkdownReport', error)
    throw new Error(`Failed to generate markdown report: ${error.message}`)
  }
}
