// Utility functions for medical chronology report generation
// Updated to use pre-extracted provider information

function getDepartmentEmoji(department: string): string {
  // Map departments to emojis based on the original prompt requirements
  const normalizedDept = department?.toLowerCase() || ''

  if (
    normalizedDept.includes('law enforcement') ||
    normalizedDept.includes('police')
  )
    return '🔵'
  if (normalizedDept.includes('ems') || normalizedDept.includes('ambulance'))
    return '🟢'
  if (
    normalizedDept.includes('emergency') ||
    normalizedDept.includes('ed') ||
    normalizedDept.includes('er')
  )
    return '🟩'
  if (
    normalizedDept.includes('imaging') ||
    normalizedDept.includes('radiology')
  )
    return '🟦'
  if (normalizedDept.includes('surgery') || normalizedDept.includes('surgical'))
    return '🟨'
  if (
    normalizedDept.includes('rehab') ||
    normalizedDept.includes('physical therapy') ||
    normalizedDept.includes('pt')
  )
    return '🟪'

  // Default to medical treatment
  return '🟨'
}

function formatDateRange(start: Date, end: Date): string {
  const startStr = new Date(start).toLocaleDateString()
  const endStr = new Date(end).toLocaleDateString()
  return startStr === endStr ? startStr : `${startStr} - ${endStr}`
}

function formatPageRange(pageRange: any): string {
  // Handle null, undefined, or empty values
  if (!pageRange) return '1'

  // If it's already a string, try to parse it
  if (typeof pageRange === 'string') {
    // Check if it's a JSON array string
    try {
      const parsed = JSON.parse(pageRange)
      if (Array.isArray(parsed)) {
        pageRange = parsed
      } else {
        return pageRange // Return as-is if it's a simple string
      }
    } catch {
      return pageRange // Return as-is if JSON parsing fails
    }
  }

  if (Array.isArray(pageRange)) {
    // Remove any non-numeric values and duplicates
    const validPages = [
      ...new Set(
        pageRange
          .map((p) => (typeof p === 'string' ? parseInt(p, 10) : Number(p)))
          .filter((p) => !isNaN(p) && p > 0)
      )
    ].sort((a, b) => a - b)

    if (validPages.length === 0) return '1'
    if (validPages.length === 1) return String(validPages[0])

    // Group consecutive pages into ranges
    const ranges: string[] = []
    let start = validPages[0]
    let end = validPages[0]

    for (let i = 1; i < validPages.length; i++) {
      if (validPages[i] === end + 1) {
        end = validPages[i]
      } else {
        ranges.push(start === end ? String(start) : `${start}-${end}`)
        start = end = validPages[i]
      }
    }
    ranges.push(start === end ? String(start) : `${start}-${end}`)
    return ranges.join(', ')
  }

  // Handle single numeric values
  const numValue = Number(pageRange)
  return isNaN(numValue) ? '1' : String(numValue)
}

// Generate treatment calendar table from events
export function generateTreatmentCalendar(
  events: {
    id: number
    binderId: string
    documentId: number | string
    pageRange: string
    event: string
    eventType: string
    eventDescription: string
    timestamp: Date
    estimatedTime: boolean
    rawExtractedData: any // Changed from string to any to access provider fields
    processed: boolean
    createdAt: Date
    updatedAt: Date
  }[]
): string {
  type TreatmentGroup = {
    provider: string
    department: string
    visits: (typeof events)[0][]
    firstVisit: Date
    lastVisit: Date
    documentPageRefs: Set<string>
  }

  const treatmentGroups = events
    .filter((e) => {
      // Exclude law enforcement entries as per original prompt
      const dept = e.rawExtractedData?.department?.toLowerCase() || ''
      return !dept.includes('law enforcement') && !dept.includes('police')
    })
    .reduce<Record<string, TreatmentGroup>>((groups, event) => {
      // Use pre-extracted provider information from rawExtractedData
      const provider =
        event.rawExtractedData?.provider && event.rawExtractedData?.facility
          ? `${event.rawExtractedData.provider} from ${event.rawExtractedData.facility}`
          : event.rawExtractedData?.provider ||
            event.rawExtractedData?.facility ||
            'Healthcare Provider'
      const department =
        event.rawExtractedData?.department || 'Medical Treatment'
      const key = `${provider}-${department}`

      // Format pageRange properly for document references
      const formattedPageRange = formatPageRange(event.pageRange)
      const documentRef = `${event.documentId}:${formattedPageRange}`

      // Debug logging - remove this in production
      console.log(
        'Event pageRange:',
        event.pageRange,
        'Formatted:',
        formattedPageRange,
        'Final ref:',
        documentRef
      )

      if (!groups[key]) {
        groups[key] = {
          provider,
          department,
          visits: [],
          firstVisit: event.timestamp,
          lastVisit: event.timestamp,
          documentPageRefs: new Set()
        }
      }

      groups[key].visits.push(event)
      groups[key].documentPageRefs.add(documentRef)

      const eventDate = new Date(event.timestamp)
      const isValidDate = (date: Date) =>
        date instanceof Date &&
        !isNaN(date.getTime()) &&
        date.getFullYear() !== 1900

      if (
        isValidDate(eventDate) &&
        (!isValidDate(new Date(groups[key].firstVisit)) ||
          eventDate < new Date(groups[key].firstVisit))
      ) {
        groups[key].firstVisit = event.timestamp
      }
      if (
        isValidDate(eventDate) &&
        (!isValidDate(new Date(groups[key].lastVisit)) ||
          eventDate > new Date(groups[key].lastVisit))
      ) {
        groups[key].lastVisit = event.timestamp
      }

      return groups
    }, {})

  let markdown = '## Treatment Calendar\n\n'
  markdown +=
    '| Medical Provider | Department | Treatment Period | Number of Visits | Reference Document |\n'
  markdown +=
    '|------------------|------------|------------------|------------------|--------------------|\n'

  Object.values(treatmentGroups)
    .sort(
      (a, b) =>
        new Date(a.firstVisit).getTime() - new Date(b.firstVisit).getTime()
    )
    .forEach((group: TreatmentGroup) => {
      const emoji = getDepartmentEmoji(group.department)
      const period = formatDateRange(group.firstVisit, group.lastVisit)
      const docRefs = Array.from(group.documentPageRefs)
        .filter((ref, index, self) => self.indexOf(ref) === index)
        .sort()
        .map((ref) => `[${ref}]`)
        .join(', ')

      // Debug logging - remove this in production
      console.log(
        'Group document refs:',
        Array.from(group.documentPageRefs),
        'Final formatted:',
        docRefs
      )

      markdown += `| ${group.provider} | ${emoji} ${group.department} | ${period} | ${group.visits.length} | ${docRefs} |\n`
    })

  return markdown
}

// Generate timeline markdown from events - Updated to match Treatment Timeline format
export function generateTimelineMarkdown(
  events: {
    timestamp: string
    event: string
    eventType: string
    eventDescription: string
    documentId: number | string
    pageRange: string
    provider?: string
    providerSpecialty?: string
    department?: string
    facility?: string
  }[]
): string {
  let markdown = '## Treatment Timeline\n\n'
  markdown += '### Part A - Chronological Treatment Timeline\n\n'

  // Updated headers to match the prompt specification
  markdown +=
    '| Date | Provider | Department | Treatment Type | Key Findings & Summary |\n'
  markdown +=
    '|------|----------|------------|----------------|----------------------|\n'

  // Sort events chronologically
  const sortedEvents = [...events].sort(
    (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
  )

  sortedEvents.forEach((event) => {
    const date = new Date(event.timestamp).toLocaleString()
    const provider = event.provider || event.facility || 'Healthcare Provider'
    const department = event.department || 'Medical Treatment'
    const emoji = getDepartmentEmoji(department)

    // Format treatment type from eventType
    const treatmentType = event.eventType
      .replace(/_/g, ' ')
      .replace(/\b\w/g, (l) => l.toUpperCase())

    // Escape pipe characters in content
    const escapedDescription = event.eventDescription.replace(/\|/g, '\\|')
    const formattedPageRange = formatPageRange(event.pageRange)
    const sourceRef = `[${event.documentId}:${formattedPageRange}]`

    markdown += `| ${date} | ${provider} | ${emoji} ${department} | ${treatmentType} | ${escapedDescription} ${sourceRef} |\n`
  })

  // Add Part B placeholder
  markdown += '\n### Part B - Prospective Care & Follow-Up Recommendations\n\n'
  markdown +=
    '_Analysis of prospective care will be added by the LLM based on strategy guidelines._\n'

  return markdown
}
