'use client'

import { ReactElement } from 'react'
import { ChatMessageBubble } from './chat-message-bubble'
import { Input } from '../../ui/input'
import { Icons } from '../icons'
import { cn } from '@/lib/utils'
import { Button, buttonVariants } from '../../ui/button'
import {
  ModelSelector,
  SelectDocumentsFilter,
  SelectYearFilter
} from './chat-window-form-components'
import type { ResearchStoreContent } from '@/types'
import type { Session } from 'next-auth'
import { CourtSelectorModal } from './chatwindow-court-picker'
import { useChatWindow } from '@/lib/hooks/use-chat-window'
import { ChatQueryClarificationModal } from './chat-query-clarification-modal'
import { PrivateUploadButton } from '../buttons/private-upload-button'
import { Textarea } from '../../ui/textarea'
import { ResearchType, Role } from '@prisma/client'
import { ChatBinderAssignModal } from './chat-binder-modal'

export function ChatWindow({
  user,
  researchProps,
  showFilters,
  researchType,
  namespace,
  stats = {
    available: 0,
    used: 0
  },
  emptyStateComponent,
  placeholder,
  masquerade
}: {
  user: Session['user']
  researchProps: ResearchStoreContent
  showFilters: boolean
  researchType: ResearchType
  namespace?: string
  stats?: {
    available: number
    used: number
  }
  emptyStateComponent?: ReactElement
  placeholder?: string
  showIngestForm?: boolean
  masquerade?: boolean
}) {
  const {
    messageContainerRef,
    input,
    handleInputChange,
    sendMessage,
    chatEndpointIsLoading,
    messages,
    sources,
    updateSource,
    sourceName,
    setSourceName,
    sourcesForMessages,
    model,
    setModel,
    court,
    setCourt,
    startYear,
    setStartYear,
    endYear,
    setEndYear,
    setSources,
    setInput,
    setEventQuery,
    openQueryClarification,
    setOpenQueryClarification,
    handleQueryClarification,
    questions
  } = useChatWindow({
    user: user,
    researchProps: researchProps,
    namespace: namespace,
    masquerade: masquerade,
    researchType: researchType
  })

  return (
    <div
      className={`flex flex-col items-center p-4 md:p-8 rounded grow overflow-hidden ${
        messages.length > 0 ? 'border' : ''
      }`}
    >
      <ChatQueryClarificationModal
        open={openQueryClarification}
        onOpenChange={setOpenQueryClarification}
        questions={questions}
        setInput={setInput}
        setEventQuery={setEventQuery}
      />
      {messages.length === 0 && emptyStateComponent ? emptyStateComponent : ''}
      <div
        className="flex flex-col-reverse w-full mb-4 overflow-auto transition-[flex-grow] ease-in-out"
        ref={messageContainerRef}
      >
        {messages.length > 0
          ? [...messages].reverse().map((m, i) => {
              const sourceKey = (messages.length - 1 - i).toString()
              return (
                <ChatMessageBubble
                  key={m.id}
                  researchId={researchProps.researchId}
                  message={m}
                  sources={sourcesForMessages[sourceKey]}
                  updateSource={updateSource}
                />
              )
            })
          : ''}
      </div>

      {researchType === ResearchType.private && messages.length > 0 && (
        <PrivateUploadButton
          variant="outline"
          size="sm"
          className="w-fit mr-auto rounded-full -mt-8 px-5 mb-2"
          buttonText="Upload More Documents"
        />
      )}

      {chatEndpointIsLoading && (
        <div className="mr-auto w-4/5 py-2 px-4 rounded-lg flex gap-4 items-center bg-ring xdark:bg-accent xdark:text-white text-sm">
          <Icons.spinner className="mr-1 h-4 w-4 animate-spin" />
          Thinking...
        </div>
      )}

      <div className="flex w-full flex-col mt-2">
        <div className="">
          {researchProps.researchId && (
            <ChatBinderAssignModal researchId={researchProps.researchId} />
          )}
        </div>

        <div className="flex flex-col md:flex-row w-full my-4 gap-2 justify-between">
          {showFilters && (
            <>
              {sourceName ? (
                <>
                  <span className="ml-1 bg-amber-300 xdark:bg-amber-500 pl-4 pr-4 font-semibold text-sm py-2 rounded-md truncate w-full">
                    Research exclusively on: {sourceName}
                  </span>
                  <Button
                    onClick={() => {
                      setSourceName(null)
                      setSources([])
                    }}
                    variant="destructive"
                    className="min-w-fit"
                  >
                    Clear
                  </Button>
                </>
              ) : (
                <>
                  <CourtSelectorModal
                    type={researchType}
                    region={user.region}
                    selectedCourts={court}
                    setSelectedCourts={setCourt}
                  />
                  {researchType === ResearchType.case && (
                    <>
                      <SelectYearFilter
                        type={endYear ? 'start' : undefined}
                        startYear={1900}
                        endYear={
                          endYear
                            ? Number(endYear)
                            : new Date().getFullYear() + 1
                        }
                        value={startYear || undefined}
                        setValue={setStartYear}
                      />
                      {endYear && startYear ? (
                        <SelectYearFilter
                          type="end"
                          startYear={Number(startYear)}
                          endYear={new Date().getFullYear() + 1}
                          value={endYear}
                          setValue={setEndYear}
                        />
                      ) : (
                        <Button
                          onClick={() => {
                            setEndYear('2024')
                          }}
                          variant="outline"
                          className="min-w-fit"
                        >
                          Set a Date Range
                        </Button>
                      )}
                    </>
                  )}
                  {user.userType === Role.super && (
                    <ModelSelector model={model} setModel={setModel} />
                  )}
                </>
              )}
            </>
          )}

          {researchType === ResearchType.private && (
            <SelectDocumentsFilter
              values={sources}
              setValues={setSources}
              options={researchProps.sourceLabels!.map((source) => ({
                value: source.id,
                label: source.title
              }))}
              maxVisible={3}
            />
          )}
          <div
            className={cn([
              buttonVariants({
                variant: 'outline'
              }),
              'min-w-fit'
            ])}
          >
            Credits used: {stats.used} of {stats.available}
          </div>
        </div>

        <form onSubmit={sendMessage} className="hidden w-full">
          <Input
            value={input}
            onChange={handleInputChange}
            disabled={stats.used >= stats.available}
          />

          <button
            type="submit"
            id="submit-button"
            disabled={chatEndpointIsLoading}
          >
            Ask
          </button>
        </form>

        <form
          id="chat-form-question-input"
          className="flex w-full"
          onSubmit={(e) => {
            e.preventDefault()
            if (user.settings.questionAssessment && !masquerade) {
              handleQueryClarification()
            } else {
              sendMessage(e)
            }
          }}
        >
          <Textarea
            className="grow mr-4 rounded"
            value={input}
            id="chat-input"
            placeholder={placeholder ?? "What's it like to be a pirate?"}
            onChange={handleInputChange}
            disabled={stats.used >= stats.available}
          />

          <Button
            variant={stats.used >= stats.available ? 'destructive' : 'default'}
            type="submit"
            className={'min-w-fit'}
            disabled={chatEndpointIsLoading}
          >
            {chatEndpointIsLoading && (
              <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
            )}
            <span>
              {stats.used >= stats.available ? 'Out of credits' : 'Ask'}
            </span>
          </Button>
        </form>
      </div>
    </div>
  )
}
