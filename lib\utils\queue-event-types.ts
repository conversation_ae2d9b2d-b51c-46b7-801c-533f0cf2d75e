/**
 * Utility functions for managing queue event types with environment-specific suffixes
 */

/**
 * Adds "_development" suffix to event type if in development environment
 */
export function getEventType(baseType: string): string {
  const isDevelopment = process.env.NODE_ENV === 'development'
  console.log(
    `Using event type: ${baseType}${isDevelopment ? '_development' : ''}`
  )

  return isDevelopment ? `${baseType}_development` : baseType
}

/**
 * Gets the base event type by removing the "_development" suffix if present
 */
export function getBaseEventType(eventType: string): string {
  return eventType.replace(/_development$/, '')
}

/**
 * Gets all possible event type variations (base type and development type)
 * Useful for querying events that could be in either format
 */
export function getEventTypeVariations(baseType: string): string[] {
  return [baseType, `${baseType}_development`]
}

/**
 * Common event types used in the application
 */
export const EVENT_TYPES = {
  DOCUMENT_PARSE: 'document-parse',
  MEDICAL_CHRONOLOGY: 'medical-chronology',
  CASE_EVALUATION: 'case-evaluation',
  DEMAND_LETTER: 'demand-letter'
} as const
