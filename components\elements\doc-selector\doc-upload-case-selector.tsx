'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from '@/components/ui/tabs'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { DocumentTitle } from '@/types/case'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-react'
import { useEffect } from 'react'

interface Props {
  title: string
  docTypes: string[]
  allDocuments: DocumentTitle[]
  isPolling?: boolean
  selectedDocumentsByType: Record<string, string[]>
  setSelectedDocumentsByType: React.Dispatch<
    React.SetStateAction<Record<string, string[]>>
  >
}

export function CaseDocumentSelector({
  title,
  docTypes,
  allDocuments,
  isPolling = false,
  selectedDocumentsByType,
  setSelectedDocumentsByType
}: Props) {
  const [activeTab, setActiveTab] = useState<string>(docTypes[0] ?? '')
  const [animatedDots, setAnimatedDots] = useState('.')

  const [searchQueryByType, setSearchQueryByType] = useState<
    Record<string, string>
  >(() => {
    const initial: Record<string, string> = {}
    docTypes.forEach((type) => {
      initial[type] = ''
    })
    return initial
  })

  const [visibleCountByType, setVisibleCountByType] = useState<
    Record<string, number>
  >(() => {
    const initial: Record<string, number> = {}
    docTypes.forEach((type) => {
      initial[type] = 10 // default initial visible count
    })
    return initial
  })

  const isDocumentSelected = (docType: string, docId: string) => {
    return selectedDocumentsByType[docType]?.includes(docId)
  }

  const handleCheckboxChange = (docType: string, docId: string) => {
    const currentSelection = selectedDocumentsByType[docType] || []
    const updated = currentSelection.includes(docId)
      ? // remove if it already exists
        currentSelection.filter((id) => id !== docId)
      : // add if it does not exist
        [...currentSelection, docId]

    setSelectedDocumentsByType((prev) => ({
      ...prev,
      [docType]: updated
    }))
  }

  const handleSearchChange = (
    docType: string,
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value
    setSearchQueryByType((prev) => ({
      ...prev,
      [docType]: value
    }))

    // Optionally reset visible count to 10 whenever the search changes
    setVisibleCountByType((prev) => ({
      ...prev,
      [docType]: 10
    }))
  }

  const handleShowMore = (docType: string) => {
    setVisibleCountByType((prev) => ({
      ...prev,
      [docType]: prev[docType] + 10
    }))
  }

  const handleShowAll = (docType: string, totalCount: number) => {
    setVisibleCountByType((prev) => ({
      ...prev,
      [docType]: totalCount
    }))
  }

  const handleSelectAll = (
    docType: string,
    documentsForType: DocumentTitle[]
  ) => {
    const allDocIds = documentsForType
      .filter((doc) => doc.indexed !== false) // only select indexed documents
      .map((doc) => String(doc.id))

    setSelectedDocumentsByType((prev) => ({
      ...prev,
      [docType]: allDocIds
    }))
  }

  const handleDeselectAll = (docType: string) => {
    setSelectedDocumentsByType((prev) => ({
      ...prev,
      [docType]: []
    }))
  }

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimatedDots((prev) => {
        if (prev === '.') return '..'
        if (prev === '..') return '...'
        return '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [])

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>
          {isPolling ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              Documents are being processed. Please wait{animatedDots}
            </div>
          ) : (
            'Select documents from the list below to add to the case.'
          )}
        </CardDescription>
      </CardHeader>

      <CardContent>
        {/* Wrap everything in Tabs (vertical orientation) */}
        <Tabs
          orientation="vertical"
          value={activeTab}
          onValueChange={(val) => setActiveTab(val)}
          className="flex space-x-4"
        >
          {docTypes.length > 1 && (
            <TabsList className="flex flex-col h-auto justify-start px-2 py-5">
              {docTypes.map((type) => (
                <TabsTrigger key={type} value={type} className="w-full">
                  {type}
                </TabsTrigger>
              ))}
            </TabsList>
          )}

          {docTypes.map((type) => {
            const searchQuery = searchQueryByType[type] || ''
            const visibleCount = visibleCountByType[type] || 10

            // Filter docs by this type + search query
            const documentsForType = allDocuments.filter((doc) =>
              doc.title.toLowerCase().includes(searchQuery.toLowerCase())
            )

            // Limit visible docs
            const visibleDocs = documentsForType.slice(0, visibleCount)

            return (
              <TabsContent key={type} value={type} className="flex-1">
                <div className="mb-4 space-y-2">
                  <Input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => handleSearchChange(type, e)}
                    placeholder={`Search ${type}...`}
                  />

                  <div className="flex space-x-2">
                    <Button
                      onClick={() => handleSelectAll(type, documentsForType)}
                      size="sm"
                      variant="outline"
                      className="rounded-full"
                    >
                      Select All
                    </Button>
                    <Button
                      onClick={() => handleDeselectAll(type)}
                      size="sm"
                      variant="outline"
                      className="rounded-full"
                    >
                      Deselect All
                    </Button>
                  </div>
                </div>

                {visibleDocs.length > 0 ? (
                  <ul className="h-[40vh] overflow-y-scroll">
                    {visibleDocs.map((document) => (
                      <li
                        key={document.id}
                        className={
                          document.indexed === false
                            ? 'flex items-center space-x-2 my-1 mr-2 list-none p-2 cursor-not-allowed'
                            : 'flex items-center space-x-2 my-1 mr-2 list-none hover:cursor-pointer hover:bg-amber-100 xdark:hover:bg-gray-700 p-2 rounded-lg duration-100'
                        }
                      >
                        <Checkbox
                          id={String(document.id)}
                          checked={isDocumentSelected(
                            type,
                            String(document.id)
                          )}
                          disabled={document.indexed === false}
                          onCheckedChange={() =>
                            handleCheckboxChange(type, String(document.id))
                          }
                        />
                        <label
                          htmlFor={String(document.id)}
                          className={cn('text-sm font-medium', {
                            'text-gray-500 xdark:text-gray-300':
                              document.indexed === false,
                            'cursor-not-allowed': document.indexed === false
                          })}
                        >
                          {document.title}{' '}
                          {document.indexed === false && (
                            <Badge variant="warning" className="ml-2 w-24">
                              Processing{animatedDots}
                            </Badge>
                          )}
                        </label>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-gray-500 my-2">
                    No documents found.
                  </p>
                )}

                {visibleCount < documentsForType.length && (
                  <div className="mt-4 space-x-2">
                    <Button
                      onClick={() => handleShowMore(type)}
                      size="sm"
                      className="rounded-full"
                    >
                      Show More
                    </Button>
                    <Button
                      onClick={() =>
                        handleShowAll(type, documentsForType.length)
                      }
                      size="sm"
                      className="rounded-full"
                    >
                      Show All
                    </Button>
                  </div>
                )}
              </TabsContent>
            )
          })}
        </Tabs>
      </CardContent>
    </Card>
  )
}
