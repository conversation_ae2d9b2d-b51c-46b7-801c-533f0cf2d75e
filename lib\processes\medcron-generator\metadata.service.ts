import { db } from '../../db'
import { createGeminiCompletion } from '../../services/gemini-service'
import { logger } from '../utils-llm'
import { PlaintiffInfo, IncidentDetails } from '@/types/case'
import { GeminiModel } from '@/types'
import { estimateTokenCount } from './utils'
import { TOP_EVENTS_FOR_METADATA, SAFE_TOKEN_LIMIT } from './constants'
import { AuthUser } from 'next-auth'

// Step 2: Structure metadata fields
export async function structureMetadata(
  extractedData: Record<string, any>,
  user: AuthUser
): Promise<{ plaintiffInfo: PlaintiffInfo; incidentDetails: IncidentDetails }> {
  logger.start('structureMetadata')

  try {
    // Filter to top 100 events by relevance score for metadata extraction

    // Collect all events from extracted data
    const allEvents: any[] = []
    Object.values(extractedData).forEach((docs: any[]) => {
      docs.forEach((doc: any) => {
        if (doc.events && Array.isArray(doc.events)) {
          allEvents.push(
            ...doc.events.map((e: any) => ({
              ...e,
              documentTitle: doc.title,
              documentType: doc.docType
            }))
          )
        }
      })
    })

    // Sort by relevance score and take top events
    const topEvents = allEvents
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
      .slice(0, TOP_EVENTS_FOR_METADATA)

    logger.info(
      `📊 Filtering ${allEvents.length} events to top ${topEvents.length} by relevance for metadata extraction`
    )

    // Create a filtered data structure with only top events
    const filteredData = {
      totalEvents: allEvents.length,
      topEventsForMetadata: topEvents,
      eventTypes: [...new Set(topEvents.map((e) => e.eventType))],
      dateRange: {
        earliest: topEvents.reduce(
          (min, e) =>
            new Date(e.timestamp) < new Date(min) ? e.timestamp : min,
          topEvents[0]?.timestamp || new Date().toISOString()
        ),
        latest: topEvents.reduce(
          (max, e) =>
            new Date(e.timestamp) > new Date(max) ? e.timestamp : max,
          topEvents[0]?.timestamp || new Date().toISOString()
        )
      }
    }

    const structureMetadataPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_STRUCTURE_METADATA'
      }
    })

    const prompt =
      structureMetadataPrompt!.prompt.replace(
        '{{consolidatedData}}',
        JSON.stringify(filteredData)
      )! + structureMetadataPrompt!.expectedOutput!

    // Check token limit
    const estimatedTokens = estimateTokenCount(prompt)
    if (estimatedTokens > SAFE_TOKEN_LIMIT) {
      logger.info(
        `⚠️ Token limit warning in structureMetadata: ${estimatedTokens} tokens exceeds safe limit of ${SAFE_TOKEN_LIMIT}. Reducing data size.`
      )
      // Further reduce events if needed
      filteredData.topEventsForMetadata =
        filteredData.topEventsForMetadata.slice(0, 50)
    }

    const response = await createGeminiCompletion({
      modelName: GeminiModel.Gemini25Pro,
      systemInstruction: prompt,
      message:
        'Extract and structure plaintiff and incident metadata for legal demand',
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'metadata-structuring'
    })

    // Parse the structured metadata from the response
    const metadata =
      typeof response === 'string' ? JSON.parse(response) : response

    // Ensure the returned data has the expected structure
    const plaintiffInfo: PlaintiffInfo = {
      fullName: metadata.plaintiffInfo.fullName || 'Unknown',
      dateOfBirth: metadata.plaintiffInfo.dateOfBirth || 'Unknown',
      preExistingConditions: metadata.plaintiffInfo.preExistingConditions || []
    }

    const incidentDetails: IncidentDetails = {
      date: metadata.incidentDetails.date || 'Unknown',
      description: metadata.incidentDetails.description || 'Unknown',
      primaryInjuries: metadata.incidentDetails.primaryInjuries || [],
      treatmentHistory: metadata.incidentDetails.treatmentHistory || ''
    }

    logger.end('structureMetadata', {
      plaintiff: plaintiffInfo.fullName,
      incidentDate: incidentDetails.date,
      eventsProcessed: topEvents.length
    })

    return { plaintiffInfo, incidentDetails }
  } catch (error: any) {
    logger.error('structureMetadata', error)
    throw new Error(`Failed to structure metadata: ${error.message}`)
  }
}

// Process user strategy inputs
export async function processUserStrategyInputs(
  strategyInputs: any,
  user: AuthUser
): Promise<any> {
  logger.start('processUserStrategyInputs')

  try {
    const processStrategyPrompt = await db.prompt.findFirst({
      where: {
        source: 'AI_MEDICAL_CHRONOLOGY_PROCESS_STRATEGY'
      }
    })

    // Convert raw form data into structured strategy that can be used by other functions
    const prompt =
      processStrategyPrompt!.prompt
        .replace(
          '{{treatmentTimelineGuidelines}}',
          strategyInputs.treatmentTimelineGuidelines || ''
        )
        .replace(
          '{{prospectiveCareGuidelines}}',
          strategyInputs.prospectiveCareGuidelines || ''
        ) + processStrategyPrompt!.expectedOutput

    const response = await createGeminiCompletion({
      modelName: GeminiModel.Gemini25Pro,
      systemInstruction: prompt,
      message:
        'Process and structure user strategy inputs for medical chronology',
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'strategy-processing'
    })

    const structuredStrategy =
      typeof response === 'string' ? JSON.parse(response) : response

    // Store the original form data for reference
    structuredStrategy.rawFormData = strategyInputs

    logger.end('processUserStrategyInputs')
    return structuredStrategy
  } catch (error: any) {
    logger.error('processUserStrategyInputs', error)
    throw new Error(`Failed to process user strategy inputs: ${error.message}`)
  }
}
