'use client'

import React, { useEffect, useState } from 'react'
import { PopupButton } from 'react-calendly'
import { buttonVariants } from '../ui/button'

interface CalendlyWidgetProps {
  calendarId: string
}

const CalendlyWidget: React.FC<CalendlyWidgetProps> = ({ calendarId }) => {
  const calendlyUrl = `https://calendly.com/${calendarId}`

  const [rootElement, setRootElement] = useState<HTMLElement | null>(null)

  useEffect(() => {
    const element = document.getElementById('__next') ?? document.body
    setRootElement(element)
  }, [])

  if (!rootElement) {
    return null
  }

  return (
    <PopupButton
      url={calendlyUrl}
      rootElement={rootElement}
      text="Book a demo"
      className={buttonVariants({
        variant: 'secondary',
        size: 'sm'
      })}
    />
  )
}

export default CalendlyWidget
