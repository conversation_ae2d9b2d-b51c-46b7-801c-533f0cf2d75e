'use client'

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { useState } from 'react'
import type { DropdownMenuCheckboxItemProps } from '@radix-ui/react-dropdown-menu'
import type { DocumentReviewResponse } from '@/app/api/gpt/review/route'

type Checked = DropdownMenuCheckboxItemProps['checked']
type ItemFilter = {
  [key: string]: Checked
}

export function ReviewDisplayCardCollection({
  segments
}: {
  segments: DocumentReviewResponse[]
}) {
  const [priorityFilter, setPriorityFilter] = useState<ItemFilter>({
    low: false,
    medium: false,
    high: false,
    all: true
  })

  const [reviewFilter, setReviewFilter] = useState<ItemFilter>({
    bad: true,
    average: true,
    good: false,
    all: false
  })

  return (
    <Card className="grid gap-5 p-5">
      <div className="flex justify-end gap-5 items-center">
        <h2>Filter Review:</h2>
        <DropdownMenuCheckboxes
          itemFilter={reviewFilter}
          setItemFilter={setReviewFilter}
        />
        <h2>Filter Priority:</h2>
        <DropdownMenuCheckboxes
          itemFilter={priorityFilter}
          setItemFilter={setPriorityFilter}
        />
      </div>
      {segments
        .filter(
          (segment) =>
            (segment.priority === 'low' && priorityFilter.low) ||
            (segment.priority === 'medium' && priorityFilter.medium) ||
            (segment.priority === 'high' && priorityFilter.high) ||
            priorityFilter.all
        )
        .filter(
          (segment) =>
            (segment.review === 'bad' && reviewFilter.bad) ||
            (segment.review === 'average' && reviewFilter.average) ||
            (segment.review === 'good' && reviewFilter.good) ||
            reviewFilter.all
        )
        .map((segment, index) => (
          <ReviewDisplayCard key={index} segment={segment} />
        ))}
    </Card>
  )
}

const ReviewDisplayCard = ({
  segment
}: {
  segment: DocumentReviewResponse
}) => {
  const reviewColor =
    segment.review === 'bad'
      ? 'text-red-500 xdark:text-red-400'
      : segment.review === 'average'
        ? 'text-yellow-500 xdark:text-yellow-400'
        : 'text-green-500 xdark:text-green-400'

  const priorityColor =
    segment.priority === 'low'
      ? 'text-blue-500 xdark:text-blue-400'
      : segment.priority === 'medium'
        ? 'text-yellow-500 xdark:text-yellow-400'
        : 'text-red-500 xdark:text-red-400'
  return (
    <Card>
      <CardHeader>
        <CardTitle>{segment.topic}</CardTitle>
        <CardDescription className={`${reviewColor} font-bold capitalize`}>
          {segment.review}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-1">
          <p>
            <span className="font-bold">Priority:</span>{' '}
            <span className={`${priorityColor} font-bold capitalize`}>
              {segment.priority}
            </span>
          </p>
          <p>
            <span className="font-bold">Reason:</span> {segment.reason}
          </p>
        </div>
      </CardContent>
    </Card>
  )
}

export function DropdownMenuCheckboxes({
  itemFilter,
  setItemFilter,
  label = 'Select at least one'
}: {
  itemFilter: ItemFilter
  setItemFilter: (value: ItemFilter) => void
  label?: string
}) {
  const filterKeys = Object.keys(itemFilter).filter((key) => key !== 'all')
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline">
          {Object.entries(itemFilter)
            .filter(([, checked]) => checked)
            .map(([key]) => key)
            .join(', ')}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <DropdownMenuLabel>{label}</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {filterKeys.map((key) => (
          <DropdownMenuCheckboxItem
            key={key}
            checked={itemFilter[key]}
            onCheckedChange={(value) =>
              setItemFilter({ ...itemFilter, [key]: value, all: false })
            }
          >
            {key}
          </DropdownMenuCheckboxItem>
        ))}
        <DropdownMenuCheckboxItem
          checked={itemFilter['all']}
          onCheckedChange={(value) =>
            setItemFilter({
              ...Object.fromEntries(filterKeys.map((key) => [key, !value])),
              all: value
            })
          }
        >
          all
        </DropdownMenuCheckboxItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
