// Maximum number of concurrent API requests for stability
export const MAX_CONCURRENT_REQUESTS = 2
export const MAX_CONSECUTIVE_ERRORS = 25

// Chunk size for document processing
export const CHUNK_SIZE = 2000

// Memory optimization constants
export const CHUNK_BATCH_SIZE = 20 // Process chunks in batches of 20
export const MAX_PARALLEL_CHUNK_BATCHES = 10 // Maximum chunk batches running in parallel (200 chunks total)
export const LATEST_EVENTS_MEMORY_LIMIT = 5 // Keep only recent events for context to manage memory

// Event filtering constants for optimization
export const MAX_EVENTS_FOR_PROCESSING = 400 // Maximum events to process in final stages (top by relevance)
export const MIN_RELEVANCE_SCORE_THRESHOLD = 0.1 // Minimum relevance score to store events

// Token limits for Gemini 2.5 Pro
export const GEMINI_25_PRO_MAX_TOKENS = 1000000 // 1M tokens
export const SAFETY_MARGIN_PERCENTAGE = 0.9 // Use 90% of max to be safe
export const SAFE_TOKEN_LIMIT =
  GEMINI_25_PRO_MAX_TOKENS * SAFETY_MARGIN_PERCENTAGE

/*
 * Parallel Processing Architecture:
 * - 2 documents processed in parallel (conservative for stability)
 * - Within each document: 10 chunk batches in parallel
 * - Each batch contains 20 chunks processed sequentially (for context preservation)
 * - Total: Up to 200 chunks can be processed simultaneously across all documents
 * - This approach provides optimal performance and memory usage
 */

// Event filtering constants for token management
export const TOP_EVENTS_FOR_METADATA = 100
export const MAX_EVENTS_FOR_GAP_ANALYSIS = 200
export const MAX_EVENTS_FOR_REPORT = 200
