'use client'

import { useState } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTableColumnHeader } from '../data-table/data-table-column-header'
import { SimpleDataTable } from './simple-data-table'
import { Prompt } from '@prisma/client'
import { EditPromptDialog } from './edit-prompt-modal'
import { PromptHistoryModal } from './prompt-history-modal'
import { Button } from '@/components/ui/button'
import { Clock } from 'lucide-react'

export function PromptList({ prompts }: { prompts: Prompt[] }) {
  // Manage the currently-editing prompt at the component level
  const [editingPromptId, setEditingPromptId] = useState<string | null>(null)
  const [historyPromptId, setHistoryPromptId] = useState<string | null>(null)

  // Create columns with access to the state setter
  const columns = getPromptListingColumns(
    setEditingPromptId,
    setHistoryPromptId
  )

  // Find the currently editing prompt
  const editingPrompt = prompts.find((prompt) => prompt.id === editingPromptId)

  return (
    <>
      <SimpleDataTable columns={columns} data={prompts} />

      {/* Edit dialog instance outside the table */}
      {editingPrompt && (
        <EditPromptDialog
          id={editingPrompt.id}
          prompt={editingPrompt}
          open={editingPromptId !== null}
          setOpen={(open) => {
            if (!open) setEditingPromptId(null)
          }}
        />
      )}

      {/* History modal instance outside the table */}
      {historyPromptId && (
        <PromptHistoryModal
          promptId={historyPromptId}
          open={historyPromptId !== null}
          onOpenChange={(open) => {
            if (!open) setHistoryPromptId(null)
          }}
        />
      )}
    </>
  )
}

// Create a function that returns the columns with access to the state setter
export const getPromptListingColumns = (
  setEditingPromptId: (id: string | null) => void,
  setHistoryPromptId: (id: string | null) => void
): ColumnDef<any>[] => [
  {
    accessorKey: 'source',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Source" />
    ),
    cell: ({ row }) => {
      const updatedAt = new Date(row.original.updatedAt)
      const formattedDate = updatedAt.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
      const formattedTime = updatedAt.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })

      return (
        <div>
          <div className="font-semibold text-amber-600">
            {row.original.role} PROMPT
          </div>
          <div className="font-medium text-xs">{row.getValue('source')}</div>
          <div className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Last updated: {formattedDate} at {formattedTime}
          </div>
        </div>
      )
    }
  },
  {
    accessorKey: 'prompt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Prompt" />
    ),
    cell: ({ row }) => {
      const promptText = row.getValue('prompt') as string
      return (
        <div>
          <div className="font-semibold text-amber-600">Base Prompt:</div>
          <div className="font-mono text-sm p-2 rounded border">
            {`${promptText.substring(0, 400)}...`}
          </div>
        </div>
      )
    }
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex flex-col gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Set the ID of the prompt being edited
              setEditingPromptId(row.original.id)
            }}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Set the ID of the prompt to show history for
              setHistoryPromptId(row.original.id)
            }}
          >
            History
          </Button>
        </div>
      )
    }
  }
]
