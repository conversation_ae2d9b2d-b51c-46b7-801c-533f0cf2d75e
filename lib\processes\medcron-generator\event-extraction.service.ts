import pLimit from 'p-limit'
import { db } from '../../db'
import { createAzureCompletion } from '../../services/azure-openai-service'
import { logger } from '../utils-llm'
import { AuthUser } from 'next-auth'
import { DocumentContent } from '@/types/case'
import { MedicalChronologyStrategyFormData } from '@/components/elements/forms/medical-chronology-strategy-form'
import { GPTModel } from '@/types'
import { ExtractedEvent } from './types'
import { splitDocumentIntoChunks, chunkArray, logMemoryUsage } from './utils'
import {
  MAX_CONCURRENT_REQUESTS,
  CHUNK_BATCH_SIZE,
  MAX_PARALLEL_CHUNK_BATCHES,
  LATEST_EVENTS_MEMORY_LIMIT,
  MIN_RELEVANCE_SCORE_THRESHOLD
} from './constants'

// Main data extraction function
export async function extractDataFromDocuments(
  documents: Record<string, DocumentContent[]>,
  user?: AuthUser,
  strategyInputs?: MedicalChronologyStrategyFormData | null,
  binderId?: string
): Promise<Record<string, any>> {
  logger.start('extractDataFromDocuments')

  try {
    const extractedData: Record<string, any> = {}
    const limit = pLimit(MAX_CONCURRENT_REQUESTS)
    const extractionPromises: Promise<void>[] = []

    // Setup the structure for each document type
    for (const docType of Object.keys(documents)) {
      extractedData[docType] = []
    }

    // Create a flat list of all extraction tasks
    for (const [docType, docs] of Object.entries(documents)) {
      for (const doc of docs) {
        extractionPromises.push(
          limit(async () => {
            logger.info(
              `Extracting data from ${docType} document: "${doc.title}" (ID: ${doc.id})`
            )

            try {
              // Split document into chunks for processing
              const chunks = splitDocumentIntoChunks(doc.content)
              let allEvents: ExtractedEvent[] = []
              let latestEvents: ExtractedEvent[] = []

              logger.info(`📊 Document split into ${chunks.length} chunks`)
              logMemoryUsage(`Document ${doc.id} chunking`)

              // Process chunks in batches for optimal performance
              const chunkBatches = chunkArray(chunks, CHUNK_BATCH_SIZE)
              logger.info(
                `🔄 Processing ${chunkBatches.length} chunk batches for document ${doc.id} (up to ${MAX_PARALLEL_CHUNK_BATCHES} batches in parallel)`
              )

              const batchLimit = pLimit(MAX_PARALLEL_CHUNK_BATCHES)
              const batchPromises: Promise<void>[] = []

              for (
                let batchIndex = 0;
                batchIndex < chunkBatches.length;
                batchIndex++
              ) {
                const chunkBatch = chunkBatches[batchIndex]

                batchPromises.push(
                  batchLimit(async () => {
                    // Process chunks in this batch sequentially to maintain context
                    for (
                      let chunkIndexInBatch = 0;
                      chunkIndexInBatch < chunkBatch.length;
                      chunkIndexInBatch++
                    ) {
                      const chunk = chunkBatch[chunkIndexInBatch]
                      const chunkIndex =
                        batchIndex * CHUNK_BATCH_SIZE + chunkIndexInBatch
                      const pageReference = `${chunkIndex + 1}`

                      try {
                        const chunkEvents = await extractEventsFromChunk(
                          chunk,
                          doc.title,
                          latestEvents,
                          strategyInputs,
                          user!,
                          chunkIndex + 1, // Current chunk number (1-based)
                          chunks.length // Total chunks in document
                        )

                        // Add source document info to each event
                        const eventsWithSource = chunkEvents.map((event) => ({
                          ...event,
                          sourceDocumentId: doc.id,
                          docType,
                          pageReference
                        }))

                        allEvents.push(...eventsWithSource)

                        // Keep latest events for context (memory limit from pro version)
                        latestEvents = allEvents.slice(
                          -LATEST_EVENTS_MEMORY_LIMIT
                        )
                      } catch (chunkError) {
                        logger.error(
                          `Error processing chunk ${chunkIndex + 1} of document ${doc.id}`,
                          chunkError
                        )
                        // Continue with other chunks
                      }
                    }
                  })
                )
              }

              // Wait for all chunk batches to complete
              await Promise.all(batchPromises)
              logger.info(
                `✅ Completed processing all chunks for document ${doc.id}`
              )

              // Store events in database if binderId is provided
              if (binderId && allEvents.length > 0) {
                try {
                  // Filter events by minimum relevance score before storing
                  const relevantEvents = allEvents.filter(
                    (event) =>
                      event.relevanceScore >= MIN_RELEVANCE_SCORE_THRESHOLD
                  )

                  if (relevantEvents.length !== allEvents.length) {
                    logger.info(
                      `📊 Filtered ${allEvents.length - relevantEvents.length} events below relevance threshold ${MIN_RELEVANCE_SCORE_THRESHOLD}`
                    )
                  }

                  for (const event of relevantEvents) {
                    await db.documentEvent.create({
                      data: {
                        binderId,
                        documentId: doc.id,
                        pageRange: event.pageReference || '1',
                        event: event.event,
                        eventType: event.eventType,
                        eventDescription: event.eventDescription,
                        timestamp: new Date(event.timestamp),
                        estimatedTime: event.estimatedTime,
                        relevanceScore: event.relevanceScore,
                        rawExtractedData: event as any,
                        processed: false
                      }
                    })
                  }
                  logger.info(
                    `Stored ${relevantEvents.length}/${allEvents.length} relevant events in database for document ${doc.id}`
                  )
                } catch (dbError) {
                  logger.error(
                    `Error storing events in database for document ${doc.id}`,
                    dbError
                  )
                }
              }

              // Store extracted events in the format expected by downstream functions
              extractedData[docType].push({
                sourceDocumentId: doc.id,
                docType,
                title: doc.title,
                events: allEvents,
                // Legacy format for compatibility
                rawContent: doc.content.substring(0, 500) + '...',
                extractedEventsCount: allEvents.length
              })

              logger.info(
                `Successfully extracted ${allEvents.length} events from ${docType} document: "${doc.title}" (ID: ${doc.id})`
              )
            } catch (docError: any) {
              logger.error(`Error processing document ${doc.id}`, docError)
              // Handle document errors gracefully
              extractedData[docType].push({
                sourceDocumentId: doc.id,
                docType,
                title: doc.title,
                error: `Document processing error: ${docError.message}`,
                events: [],
                partialContent: doc.content.substring(0, 100) + '...'
              })
            }
          })
        )
      }
    }

    // Wait for all extraction tasks to complete
    await Promise.all(extractionPromises)

    logger.end('extractDataFromDocuments', {
      documentTypes: Object.keys(extractedData).length,
      totalDocuments: Object.values(extractedData).flat().length,
      totalEvents: Object.values(extractedData)
        .flat()
        .reduce((sum, doc: any) => sum + (doc.events?.length || 0), 0)
    })

    return extractedData
  } catch (error: any) {
    logger.error('extractDataFromDocuments', error)
    throw new Error(`Failed to extract data from documents: ${error.message}`)
  }
}

// Extract events from text chunk using LLM (simplified from pro version)
export async function extractEventsFromChunk(
  chunk: string,
  documentTitle: string,
  latestEvents: ExtractedEvent[],
  strategyInputs: MedicalChronologyStrategyFormData | null | undefined,
  user: AuthUser,
  chunkNumber?: number,
  totalChunks?: number,
  retryCount: number = 0
): Promise<ExtractedEvent[]> {
  // Fetch the event extraction prompt from database
  const eventExtractionPrompt = await db.prompt.findFirst({
    where: {
      source: 'AI_MEDICAL_CHRONOLOGY_EVENT_EXTRACTION'
    }
  })

  if (!eventExtractionPrompt) {
    throw new Error(
      'Event extraction prompt not found in database. Please ensure AI_MEDICAL_CHRONOLOGY_EVENT_EXTRACTION prompt is properly configured.'
    )
  }

  // Build context sections for variable replacement
  const latestEventsSection =
    latestEvents.length > 0
      ? 'Recent events:\n' +
        latestEvents.map((e) => `- ${e.event} (${e.timestamp})`).join('\n')
      : 'No recent events.'

  const strategyGuidelines = [
    strategyInputs?.treatmentTimelineGuidelines?.trim()
      ? `STRATEGY GUIDANCE - Treatment Timeline Guidelines:\n${strategyInputs.treatmentTimelineGuidelines}\n\nFollow these specific treatment timeline guidelines when extracting events.`
      : '',
    strategyInputs?.prospectiveCareGuidelines?.trim()
      ? `STRATEGY GUIDANCE - Prospective Care & Follow-Up Guidelines:\n${strategyInputs.prospectiveCareGuidelines}\n\nWhen extracting events related to future care, recommendations, or follow-up treatment, prioritize according to these prospective care guidelines.`
      : ''
  ]
    .filter(Boolean)
    .join('\n\n')

  // Replace variables in the prompt template
  const prompt = eventExtractionPrompt.prompt
    .replace('{{documentTitle}}', documentTitle)
    .replace('{{chunk}}', chunk)
    .replace('{{latestEventsSection}}', latestEventsSection)
    .replace('{{strategyGuidelines}}', strategyGuidelines)

  try {
    const progressInfo =
      chunkNumber && totalChunks ? ` (${chunkNumber}/${totalChunks})` : ''
    console.log(
      `📄 Extracting events from chunk${progressInfo} - ${documentTitle}`
    )

    const response = await createAzureCompletion({
      messages: [
        {
          role: 'system',
          content: prompt + '\n' + eventExtractionPrompt.expectedOutput
        }
      ],
      model: GPTModel.GPTo4Mini,
      json: true,
      teamId: user.teamId,
      purpose: 'med-cron',
      activity: 'event-extraction'
    })

    const events: ExtractedEvent[] = response.events || []

    // Validate and filter events
    const validEvents = events.filter((event) => {
      try {
        // Validate timestamp format
        const date = new Date(event.timestamp)
        const hasValidDate = !isNaN(date.getTime())
        const hasRequiredFields = event.event && event.eventDescription
        const hasValidRelevanceScore =
          typeof event.relevanceScore === 'number' &&
          event.relevanceScore >= 0.0 &&
          event.relevanceScore <= 1.0

        return hasValidDate && hasRequiredFields && hasValidRelevanceScore
      } catch {
        return false
      }
    })

    return validEvents
  } catch (error: any) {
    logger.error('extractEventsFromChunk', error)

    // Retry once if this is the first attempt
    if (retryCount === 0) {
      const progressInfo =
        chunkNumber && totalChunks ? ` (${chunkNumber}/${totalChunks})` : ''
      logger.info(
        `Retrying extraction for chunk${progressInfo} - ${documentTitle} (attempt 2/2)`
      )
      try {
        return await extractEventsFromChunk(
          chunk,
          documentTitle,
          latestEvents,
          strategyInputs,
          user,
          chunkNumber,
          totalChunks,
          retryCount + 1
        )
      } catch (retryError: any) {
        logger.error('extractEventsFromChunk retry failed', retryError)
      }
    }

    // Return empty array on error to prevent pipeline failure
    return []
  }
}
