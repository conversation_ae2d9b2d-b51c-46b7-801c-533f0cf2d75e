'use server'

import { CaseFileType } from '@prisma/client'
import { db } from '../../db'
import { UnauthorizedError } from '../../exceptions'
import { getCurrentUser } from '../../session'
import { generateCaseEvaluationLite } from '@/lib/processes/case-eval-lite-generator'
import { DocumentContent } from '@/types/case'
import {
  notifyCaseProcessInitiated,
  notifyCaseProcessCompleted,
  notifyCaseProcessFailed,
  createCaseEventData
} from '../../services/case-notifications'

export async function handleCaseEvaluationLiteGeneration({
  binderId,
  selectedDocumentsByType
}: {
  binderId: string
  selectedDocumentsByType: Record<string, string[]>
}) {
  const startTime = Date.now()
  let user: any = null
  let caseFileId = ''

  try {
    user = await getCurrentUser()

    if (!user) {
      throw new UnauthorizedError('User not found')
    }

    // Extract all document IDs and convert to numbers
    const allDocumentIds = Object.values(selectedDocumentsByType)
      .flat()
      .map(Number)

    // Send Slack notification for process initiation
    const documentCount = allDocumentIds.length
    await notifyCaseProcessInitiated(
      createCaseEventData('case-eval', binderId, user, {
        documentCount
      })
    )

    // Fetch all documents in a single query
    const documents = await db.documentRecords.findMany({
      where: {
        id: {
          in: allDocumentIds
        }
      },
      select: {
        id: true,
        title: true,
        indexed: true,
        content: true
      }
    })

    // Map content by document type
    const documentContentByType = Object.fromEntries(
      Object.entries(selectedDocumentsByType).map(([docType, docIds]) => [
        docType,
        documents.filter((doc) => docIds.includes(doc.id.toString()))
      ])
    )

    // Generate the case evaluation lite
    const caseEvaluationLite = await generateCaseEvaluationLite(
      binderId,
      documentContentByType,
      user
    )

    // Store the case evaluation lite in the database
    const caseFile = await db.caseFile.upsert({
      where: {
        binderId_fileType: {
          binderId,
          fileType: CaseFileType.CASE_EVALUATION
        }
      },
      update: {
        content: caseEvaluationLite,
        selectedDocumentsByType,
        updatedAt: new Date()
      },
      create: {
        binderId,
        creatorId: user.id,
        fileType: CaseFileType.CASE_EVALUATION,
        content: caseEvaluationLite,
        selectedDocumentsByType
      }
    })

    caseFileId = caseFile.id

    // Track credit usage
    await db.teamCreditUsed.create({
      data: {
        teamId: user.teamId,
        type: 'case',
        refId: caseFile.id,
        eventId: new Date().getTime().toString()
      }
    })

    // Send success notification
    const duration = Date.now() - startTime
    await notifyCaseProcessCompleted(
      createCaseEventData('case-eval', binderId, user, {
        processId: caseFileId,
        duration
      })
    )

    return {
      success: true,
      caseEvaluation: caseFile
    }
  } catch (error) {
    console.error('Failed to generate case evaluation lite:', error)

    // Send failure notification
    try {
      if (user) {
        const duration = Date.now() - startTime
        await notifyCaseProcessFailed(
          createCaseEventData('case-eval', binderId, user, {
            processId: caseFileId || 'unknown',
            duration,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        )
      }
    } catch (notifError) {
      console.error('Failed to send failure notification:', notifError)
    }

    return {
      success: false,
      error: 'Failed to generate case evaluation lite'
    }
  }
}
