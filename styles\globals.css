@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 48 26% 85%;
    /* Light beige background (#e8e3ce) */
    --foreground: 222.2 47.4% 11.2%;
    /* Dark text color */

    --muted: 210 20% 95%;
    /* Light gray */
    --muted-foreground: 222.2 47.4% 40%;
    /* Darker gray */

    --popover: 0 0% 100%;
    /* Pure white */
    --popover-foreground: 222.2 47.4% 11.2%;
    /* Dark text color */

    --border: 210 20% 90%;
    /* Light gray */
    --input: 210 20% 90%;
    /* Light gray */

    --card: 0 0% 100%;
    /* Pure white */
    --card-foreground: 222.2 47.4% 11.2%;
    /* Dark text color */

    --primary: 50 100% 50%;
    /* Golden yellow */
    --primary-foreground: 0 0% 100%;
    /* White text */

    --secondary: 210 80% 50%;
    /* Soft blue */
    --secondary-foreground: 0 0% 100%;
    /* White text */

    --accent: 45 100% 40%;
    /* Even more darker gold */
    --accent-foreground: 0 0% 100%;
    /* White text */

    --destructive: 0 100% 50%;
    /* Bright red */
    --destructive-foreground: 0 0% 100%;
    /* White text */

    --ring: 50 100% 50%;
    /* Golden yellow */

    --radius: 0.5rem;
    /* Border radius */
  }

  .dark {
    --background: 222.2 47.4% 11.2%;
    /* Very dark gray */
    --foreground: 0 0% 100%;
    /* White text */

    --muted: 222.2 30% 20%;
    /* Dark muted background */
    --muted-foreground: 0 0% 80%;
    /* Light gray text */

    --popover: 222.2 47.4% 11.2%;
    /* Very dark gray */
    --popover-foreground: 0 0% 100%;
    /* White text */

    --border: 222.2 30% 20%;
    /* Dark gray */
    --input: 222.2 30% 20%;
    /* Dark gray */

    --card: 222.2 47.4% 11.2%;
    /* Very dark gray */
    --card-foreground: 0 0% 100%;
    /* White text */

    --primary: 50 100% 50%;
    /* Golden yellow */
    --primary-foreground: 0 0% 100%;
    /* White text */

    --secondary: 210 80% 50%;
    /* Soft blue */
    --secondary-foreground: 0 0% 100%;
    /* White text */

    --accent: 45 100% 30%;
    /* Even more darker gold */
    --accent-foreground: 0 0% 100%;
    /* White text */

    --destructive: 0 100% 50%;
    /* Bright red */
    --destructive-foreground: 0 0% 100%;
    /* White text */

    --ring: 50 100% 50%;
    /* Golden yellow */

    --radius: 0.5rem;
    /* Border radius */
  }
}

/* .message-bubble-main span p {
  display: inline-flex;
} */

/* .message-bubble-main span * {
  font-size: 14px;
  font-weight: 500; 
} */

.message-bubble-main span {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.message-bubble-main h1 {
  margin-top: 15px;
  font-size: 18px;
  font-weight: 700;
}

/* Heading 2 */
.message-bubble-main h2 {
  margin-top: 12px;
  font-size: 17px;
  font-weight: 600;
}

/* Heading 3 */
.message-bubble-main h3 {
  margin-top: 10px;
  font-size: 16px;
  font-weight: 600;
}

/* Heading 4 */
.message-bubble-main h4 {
  margin-top: 10px;
  font-size: 15px;
  font-weight: 500;
}

/* Paragraph */
.message-bubble-main p {
  font-size: 14px;
  font-weight: 400;
}

/* List Items */
.message-bubble-main ul,
.message-bubble-main ol {
  font-size: 14px;
  font-weight: 400;
  margin-left: 1rem;
}

.message-bubble-main ol ul,
.message-bubble-main ul ul {
  margin-top: -20px;
  margin-bottom: -40px;
}

.katex-html {
  display: none !important;
}

.katex-mathml {
  background-color: rgba(0, 0, 0, 0.2); /* Darken the background by 20% */
  padding-top: 20px;
  padding-bottom: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 8px;
}

.katex-mathml mfrac mtext {
  margin-top: 5px;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
  }
}

.ProseMirror {
  line-height: 1.6; /* Default line-height for all text */
}

.ProseMirror p {
  margin-bottom: 10px; /* Add spacing between paragraphs */
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  margin-top: 20px;
  margin-bottom: 10px;
  line-height: 1.4; /* Adjust for headings */
  font-weight: 600; /* Bold headings */
}
