import React, { useState } from 'react'
import { NodeViewWrapper, NodeViewProps } from '@tiptap/react'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  Sheet<PERSON><PERSON><PERSON>,
  SheetTrigger
} from '@/components/ui/sheet'
import { useEditorContext } from '@/lib/context/tiptapContext'

// Simple citation icon component
const CitationIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className={className}
  >
    <path d="M7 15h1.5a1.5 1.5 0 0 0 0-3H7M9 6h5.5a1.5 1.5 0 0 1 0 3H9M6 21v-7"></path>
    <rect width="18" height="18" x="3" y="3" rx="3"></rect>
  </svg>
)

export const CitationListModal: React.FC<NodeViewProps> = (props) => {
  const { node } = props
  const { docId, page } = node.attrs
  const [open, setOpen] = useState(false)
  const { refDocuments } = useEditorContext()

  // find document
  const doc = refDocuments.find((doc) => String(doc.id) === String(docId))

  if (!doc) {
    return null
  }

  // Find the document title from refDocuments using docId string comparison
  const documentTitle = doc.title || ``

  return (
    <NodeViewWrapper
      as="span"
      data-modal-link
      data-doc-id={docId}
      data-page={page}
    >
      <Sheet open={open} onOpenChange={setOpen}>
        <SheetTrigger asChild>
          <span className="inline-flex items-center cursor-pointer text-blue-600 hover:text-blue-800">
            <CitationIcon className="inline-flex h-3.5" />
            <span className="text-sm font-medium">{documentTitle}</span>
          </span>
        </SheetTrigger>
        <SheetContent
          className="md:max-w-[80%] md:w-[80%] bg-background xdark:bg-background"
          side="right"
        >
          <SheetHeader>
            <SheetTitle>{documentTitle}</SheetTitle>
            <SheetDescription>
              <iframe
                src={`/document-viewer/${docId}${page ? `?page=${page}` : ''}`}
                className="w-full h-[90vh]"
              ></iframe>
            </SheetDescription>
          </SheetHeader>
        </SheetContent>
      </Sheet>
    </NodeViewWrapper>
  )
}
