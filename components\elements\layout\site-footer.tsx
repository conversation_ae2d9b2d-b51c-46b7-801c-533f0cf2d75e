import { cn } from '@/lib/utils'
import { NavItem } from '@/types'
import Link from 'next/link'

interface FooterNavProps {
  items?: NavItem[]
  home?: string
  className?: string
}

export function SiteFooter({ items, className }: FooterNavProps) {
  return (
    <footer className={cn(className)}>
      <div className="text-background max-w-[90%] container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
        {/* <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          {items?.length ? (
            <nav className="hidden gap-6 lg:flex">
              {items?.map((item, index) => (
                <Link
                  key={index}
                  href={item.disabled ? '#' : item.href}
                  className={cn(
                    'flex items-center text-lg font-medium transition-colors hover:text-amber-300 sm:text-sm',
                    item.disabled && 'cursor-not-allowed opacity-80'
                  )}
                >
                  {item.title}
                </Link>
              ))}
            </nav>
          ) : null}
        </div> */}
        <div className="w-full text-center text-xs text-amber-50">
          © {new Date().getFullYear()} Smart Counsel AI. All rights reserved.
        </div>
      </div>
    </footer>
  )
}
