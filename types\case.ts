import { AttorneyInsightsFormData } from '@/components/elements/doc-selector/attorney-strategy-form'
import { type MedicalChronologyStrategyFormData } from '@/components/elements/forms/medical-chronology-strategy-form'

import { ExtractedCaseFinancialData } from '@/lib/processes/case-eval-generator/financial-extraction-types'
import { CaseFileType, DocumentRecords } from '@prisma/client'

export interface CaseBinderData {
  attorney: string
  caseNumber?: string
}

export type DocumentContent = Pick<
  DocumentRecords,
  'id' | 'title' | 'indexed' | 'content'
>
export type DocumentTitle = Pick<DocumentRecords, 'id' | 'indexed' | 'title'>

// Define context data interface
export type ContextData = {
  [key in CaseFileType]?: string
}

// Define section structure
export interface GeneratedSection {
  id: string
  title: string
  content: string
}

// Define the complete demand letter structure
export interface DemandLetter {
  caseId: string
  sections: GeneratedSection[]
  combinedContent: string
  metadata: {
    generatedAt: string
    tokenCount: number
    generationTimeMs: number
  }
}

export interface PromptStore {
  id: string
  title: string
  contextNeeded: CaseFileType[]
  prompt: string
}

export interface PlaintiffInfo {
  fullName: string
  dateOfBirth: string
  preExistingConditions: string[]
}

export interface IncidentDetails {
  date: string
  description: string
  primaryInjuries: string[]
  treatmentHistory: string
}

export interface ChronologyEvent {
  date: string
  category: EventCategory
  title: string
  summary: string
  sourceDocumentId: number
  sourcePageReferences: string
  provider?: string
  providerSpecialty?: string
  department?: string
  facility?: string
}

export enum EventCategory {
  PoliceReport = 'PoliceReport',
  AmbulanceReport = 'AmbulanceReport',
  EDAdmission = 'EDAdmission',
  ImagingDiagnostics = 'ImagingDiagnostics',
  Treatment = 'Treatment',
  EDDischarge = 'EDDischarge',
  FollowUp = 'FollowUp'
}

export interface SourceLink {
  eventId: string
  documentId: number
  pageReferences: string
  url?: string
}

export interface CaseGap {
  type: string
  description: string
  recommendation: string
  severity: 'Low' | 'Medium' | 'High'
}

export interface MedicalChronologyBase {
  plaintiffInfo: PlaintiffInfo
  incidentDetails: IncidentDetails
  events: ChronologyEvent[]
  sourceLinks: SourceLink[]
  caseGaps: CaseGap[]
}

export interface MedicalChronology extends MedicalChronologyBase {
  markdownReport: string
  strategyInputs?: MedicalChronologyStrategyFormData
}

export interface Document {
  id: number
  title: string
  content: string
}

// Case evaluation interfaces
export interface PlaintiffInfo {
  fullName: string
  dateOfBirth: string
}

export interface IncidentDetails {
  date: string
  description: string
  primaryInjuries: string[]
  treatmentHistory: string
}

export interface LiabilityAssessment {
  faultDistribution: {
    plaintiff: number
    defendant: number
    other?: number
  }
  supportingEvidence: string[]
  potentialDefenses: string[]
  legalTheory: string
  jurisdictionSpecifics?: string
}

export interface EconomicDamages {
  medicalExpenses: {
    past: number
    future: number
    details: string
  }
  lostWages: {
    past: number
    future: number
    details: string
  }
  propertyDamage: number
  householdServiceLoss: number
  otherEconomicLosses: { [key: string]: number }
  total: number
}

export interface NonEconomicDamages {
  painAndSuffering: {
    multiplier: number
    amount: number
    justification: string
  }
  emotionalDistress?: {
    amount: number
    justification: string
  }
  lossOfEnjoyment?: {
    amount: number
    justification: string
  }
  disfigurement?: {
    amount: number
    justification: string
  }
  total: number
}

export interface PunitiveDamagesData {
  eligibility: boolean
  justification?: string
  recommendedMultiplier?: number
  estimatedAmount?: number
}

export interface DamagesCalculation {
  economic: number
  nonEconomic: number
  punitive?: number
  total: number
  reductionForComparativeFault?: number
  finalEstimate: {
    low: number
    expected: number
    high: number
  }
}

export interface RiskAssessment {
  caseStrengths: string[]
  caseWeaknesses: string[]
  mitigationStrategies: { [key: string]: string }
  overallRisk: 'Low' | 'Medium' | 'High'
  recommendedExhibits: string[]
  challengingEvidence: string[]
}

export interface LitigationStrategy {
  settlementRecommendation: {
    minAcceptable: number
    target: number
    timing: string
    approach: string
  }
  trialStrategy?: {
    keyThemes: string[]
    witnessOrder: string[]
    expertRecommendations: string[]
    juryConsiderations: string[]
  }
  timelineRecommendation: string
}

export interface DefensePerspective {
  vulnerabilities: {
    issue: string
    category: string
    severity: 'Low' | 'Medium' | 'High'
    potentialImpact: string
  }[]
  counterStrategies: {
    vulnerability: string
    strategy: string
    implementation: string
  }[]
  overallAssessment: string
}

export interface CaseEvaluation {
  plaintiffInfo: PlaintiffInfo
  incidentDetails: IncidentDetails
  liabilityAssessment: LiabilityAssessment
  economicDamages: EconomicDamages
  nonEconomicDamages: NonEconomicDamages
  punitiveDamagesData: PunitiveDamagesData
  damagesCalculation: DamagesCalculation
  riskAssessment: RiskAssessment
  litigationStrategy: LitigationStrategy
  initialMarkdownReport: string
  defensePerspective?: DefensePerspective
  revisedMarkdownReport?: string
  extractedFinancialData?: ExtractedCaseFinancialData
  attorneyInsights?: AttorneyInsightsFormData
}

// Configuration options for case evaluation
export interface CaseEvaluationOptions {
  includeDefensePerspective?: boolean
  nonEconomicMultiplierOverride?: number
  punitiveMultiplierOverride?: number
}
