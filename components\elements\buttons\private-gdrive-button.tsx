'use client'

import React, { useState, useCallback, useEffect } from 'react'
import { Button, ButtonProps } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import Image from 'next/image'
import { getGoogleDrivePDFfiles } from '@/lib/actions/google-drive'
import { drive_v3 } from 'googleapis'
import { toast } from '../../ui/use-toast'
import { Skeleton } from '../../ui/skeleton'
import { PrivateGDriveTable } from '../private-gdrive-table'

interface PrivateGDriveButtonProps extends ButtonProps {
  buttonText?: string
  className?: string
}

export function PrivateGDriveButton({
  buttonText = 'Sync Google Drive',
  className,
  ...props
}: PrivateGDriveButtonProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant={'secondary'}
          className={cn('min-w-fit flex gap-2', className)}
          {...props}
        >
          <Image
            className="w-5 h-5"
            src="/props/icons/gdrive.png"
            alt="Google Drive"
            width={100}
            height={100}
          />
          {buttonText}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-max">
        <DialogHeader>
          <DialogTitle className="flex gap-3">
            <Image
              className="w-8 h-8"
              src="/props/icons/gdrive.png"
              alt="Google Drive"
              width={100}
              height={100}
            />
            Sync Google Drive
          </DialogTitle>
          <DialogDescription>
            Sync your documents from Google Drive to SmartCounsel.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <FileListComponent />
        </div>
      </DialogContent>
    </Dialog>
  )
}

function FileListComponent() {
  const [availableFiles, setAvailableFiles] = useState<drive_v3.Schema$File[]>(
    []
  )
  const [loading, setLoading] = useState(false)
  const loadUserFileUploads = useCallback(async () => {
    setLoading(true)
    try {
      const fileUploads = await getGoogleDrivePDFfiles()

      if (fileUploads && 'revalidate' in fileUploads) {
        window.location.replace(fileUploads.revalidate)
      } else if (fileUploads) {
        setAvailableFiles(fileUploads)
      }
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'An error occurred while fetching files',
        variant: 'destructive'
      })
    }
    setLoading(false)
  }, [])

  useEffect(() => {
    void loadUserFileUploads()
  }, [])

  return (
    <ul className="divide-y divide-gray-200 xdark:divide-gray-700 space-y-3">
      {loading ? (
        [...Array(5)].map((_, index) => (
          <li key={index}>
            <Skeleton className="h-5" />
          </li>
        ))
      ) : (
        <PrivateGDriveTable
          documents={availableFiles.map((file) => ({
            id: file.id || '',
            title: file.name || ''
          }))}
        />
      )}
    </ul>
  )
}
