/**
 * Send notification to user-activities Slack channel for case-related events.
 * @param {string} textMsg - Message that needs to be sent. Refer doc for formatting: https://api.slack.com/reference/surfaces/formatting
 */
export const sendUserActivityNotification = async (textMsg: string) => {
  try {
    const webhookUrl =
      '*********************************************************************************'
    const payload = {
      text: textMsg
    }

    await fetch(webhookUrl, {
      method: 'POST',
      body: JSON.stringify(payload),
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error sending user activity notification:', error)
  }
}

/**
 * Send notification to signups Slack channel for new user registrations.
 * @param {string} textMsg - Message that needs to be sent. Refer doc for formatting: https://api.slack.com/reference/surfaces/formatting
 */
export const sendUserRegistrationNotification = async (textMsg: string) => {
  try {
    const webhookUrl =
      '*********************************************************************************'
    const payload = {
      text: textMsg
    }

    await fetch(webhookUrl, {
      method: 'POST',
      body: JSON.stringify(payload),
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('Error sending user registration notification:', error)
  }
}

/**
 * @deprecated Use sendUserActivityNotification or sendUserRegistrationNotification instead
 * Legacy function for backward compatibility
 */
export const sendMsgOnSlack = async ({
  channel = 'user-activities',
  textMsg
}: {
  channel?: string
  textMsg: string
}) => {
  await sendUserActivityNotification(textMsg)
}
