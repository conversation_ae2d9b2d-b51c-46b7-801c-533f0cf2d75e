import { redirect } from 'next/navigation'

import { authOptions } from '@/lib/auth'
import { getCurrentUser } from '@/lib/session'
import { DashboardHeader } from '@/components/elements/layout/header'
import { DashboardShell } from '@/components/elements/layout/shell'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'
import { Icons } from '@/components/elements/icons'
import { features } from '@/config/dashboard'
import { Badge } from '@/components/ui/badge'

export const metadata = {
  title: 'Dashboard | SmartCounsel AI'
}

export default async function DashboardPage() {
  const user = await getCurrentUser()

  if (!user) {
    redirect(authOptions?.pages?.signIn || '/login')
  }

  const cardItems = Object.values(features)

  return (
    <DashboardShell>
      <DashboardHeader
        heading={`Hi ${user.name || 'there'}!`}
        text="Welcome to SmartCounsel AI"
      ></DashboardHeader>
      <Card className="lg:max-w-3xl">
        <CardHeader>
          <CardTitle>What would you like to do?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid text-center lg:w-full lg:grid-cols-2 lg:text-left">
            {cardItems.map((item, index) =>
              item.link ? (
                <Link
                  key={index}
                  href={item.link}
                  className="flex flex-col items-center lg:items-start group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-black border-4 hover:bg-ring/80 hover:xdark:border-neutral-700 hover:xdark:bg-neutral-800/30 cursor-pointer"
                >
                  <h2 className={`mb-3 text-2xl font-semibold`}>
                    {item.title}
                    <Icons.chevronRight className="mb-1 ml-1 inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none" />
                  </h2>
                  <p className={`m-0 max-w-[30ch] text-sm`}>
                    {item.description}
                  </p>
                </Link>
              ) : (
                <div
                  key={index}
                  className="flex flex-col items-center lg:items-start group rounded-lg border border-transparent px-5 py-4 transition-colors opacity-50"
                >
                  <h2 className="text-2xl font-semibold">{item.title}</h2>
                  <Badge variant="outline" className="my-2">
                    Coming Soon!
                  </Badge>
                  <p className="m-0 max-w-[30ch] text-sm opacity-50">
                    {item.description}
                  </p>
                </div>
              )
            )}
          </div>
        </CardContent>
      </Card>
    </DashboardShell>
  )
}
