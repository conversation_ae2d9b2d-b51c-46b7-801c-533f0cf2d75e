import { db } from '../../db'
import { logger } from '../utils-llm'
import { ChronologyEvent, DocumentContent, SourceLink } from '@/types/case'
import { mapEventTypeToCategory, findDocTypeForDocument } from './utils'
import { MAX_EVENTS_FOR_PROCESSING } from './constants'

// New function to organize events chronologically from database
export async function organizeChronologicalEventsFromDB(
  extractedData: Record<string, any>,
  binderId?: string
): Promise<ChronologyEvent[]> {
  logger.start('organizeChronologicalEventsFromDB')

  try {
    let allEvents: ChronologyEvent[] = []

    if (binderId) {
      // Fetch events from database, already sorted by timestamp
      const dbEvents = await db.documentEvent.findMany({
        where: {
          binderId,
          processed: true
        },
        orderBy: {
          timestamp: 'asc'
        },
        take: MAX_EVENTS_FOR_PROCESSING // Limit to top events
      })

      logger.info(
        `📊 Fetched ${dbEvents.length} processed events from database`
      )

      // Convert database events to ChronologyEvent format
      allEvents = dbEvents.map((event) => ({
        date: event.timestamp.toISOString(),
        title: event.event,
        summary: event.eventDescription,
        category: mapEventTypeToCategory(event.eventType),
        sourceDocumentId: event.documentId,
        sourcePageReferences: event.pageRange,
        provider: (event.rawExtractedData as any)?.provider,
        providerSpecialty: (event.rawExtractedData as any)?.providerSpecialty,
        department: (event.rawExtractedData as any)?.department,
        facility: (event.rawExtractedData as any)?.facility
      }))
    } else {
      // No binderId, extract events from extractedData
      Object.values(extractedData).forEach((docs: any[]) => {
        docs.forEach((doc: any) => {
          if (doc.events && Array.isArray(doc.events)) {
            const chronologyEvents = doc.events.map((event: any) => ({
              date: event.timestamp,
              title: event.event,
              summary: event.eventDescription,
              category: mapEventTypeToCategory(event.eventType),
              sourceDocumentId: doc.sourceDocumentId,
              sourcePageReferences: event.pageReference || '1',
              provider: event.provider,
              providerSpecialty: event.providerSpecialty,
              department: event.department,
              facility: event.facility
            }))
            allEvents.push(...chronologyEvents)
          }
        })
      })

      // Sort events by date
      allEvents.sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
      )

      // Limit events if too many
      if (allEvents.length > MAX_EVENTS_FOR_PROCESSING) {
        logger.info(
          `📊 Limiting events from ${allEvents.length} to ${MAX_EVENTS_FOR_PROCESSING}`
        )
        allEvents = allEvents.slice(0, MAX_EVENTS_FOR_PROCESSING)
      }
    }

    logger.end('organizeChronologicalEventsFromDB', {
      eventCount: allEvents.length
    })

    return allEvents
  } catch (error: any) {
    logger.error('organizeChronologicalEventsFromDB', error)
    throw new Error(`Failed to organize chronological events: ${error.message}`)
  }
}

// Step 4: Link source documents
export async function linkSourceDocuments(
  events: ChronologyEvent[],
  documents: Record<string, DocumentContent[]>
): Promise<SourceLink[]> {
  logger.start('linkSourceDocuments')

  try {
    const sourceLinks: SourceLink[] = []

    // Flatten the documents array for easier lookup
    const allDocuments = Object.values(documents).flat()

    // Create source links for each event
    for (let i = 0; i < events.length; i++) {
      const event = events[i]

      // Find the corresponding document
      const document = allDocuments.find(
        (doc) => doc.id === event.sourceDocumentId
      )

      if (document) {
        sourceLinks.push({
          eventId: `event-${i}`, // Generate an ID for the event
          documentId: document.id,
          pageReferences: event.sourcePageReferences || 'N/A'
        })
      }
    }

    logger.end('linkSourceDocuments', { linkCount: sourceLinks.length })
    return sourceLinks
  } catch (error: any) {
    logger.error('linkSourceDocuments', error)
    throw new Error(`Failed to link source documents: ${error.message}`)
  }
}
