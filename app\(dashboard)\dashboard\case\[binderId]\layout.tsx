import { AlertTriangle } from 'lucide-react'
import { db } from '@/lib/db'
import { getCurrentUser } from '@/lib/session'
import Link from 'next/link'

interface BinderDetailPageProps {
  params: {
    binderId: string
  }
  children?: React.ReactNode
}

export default async function BinderDetailLayout({
  params,
  children
}: BinderDetailPageProps) {
  const user = await getCurrentUser()

  const binder = await db.binder.findUnique({
    where: {
      id: params.binderId
    },
    select: {
      id: true,
      name: true,
      teamId: true
    }
  })

  if (!binder || !user || binder.teamId !== user.teamId) {
    return <NotYourCase />
  }

  return <>{children}</>
}

function NotYourCase() {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-100 xdark:bg-gray-900 px-4">
      <div className="max-w-md w-full bg-white xdark:bg-gray-800 border border-gray-300 xdark:border-gray-700 rounded-2xl shadow-lg p-6 flex flex-col items-center text-center space-y-4">
        <AlertTriangle className="h-10 w-10 text-yellow-600 xdark:text-yellow-400" />
        <h1 className="text-xl font-semibold text-gray-800 xdark:text-gray-100">
          Not Your Case
        </h1>
        <p className="text-sm text-gray-600 xdark:text-gray-400">
          This record doesn&apos;t seem to be associated with your client or
          claim. Please double-check the case ID or contact support if you
          believe this is a mistake.
        </p>
        <Link
          href="/dashboard"
          className="mt-4 inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md shadow transition duration-200 xdark:bg-blue-500 xdark:hover:bg-blue-600"
        >
          Return to Dashboard
        </Link>
      </div>
    </div>
  )
}
