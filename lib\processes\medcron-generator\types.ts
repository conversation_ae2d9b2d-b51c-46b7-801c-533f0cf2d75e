import { DocumentContent, EventCategory } from '@/types/case'

// Error collector utility interface
export interface ErrorCollector {
  errors: string[]
  consecutiveErrors: number
  hasError: boolean
  addError: (error: string, context?: string) => void
  reset: () => void
  shouldExitProcess: () => boolean
  getErrorSummary: () => {
    totalErrors: number
    consecutiveErrors: number
    recentErrors: string[]
    shouldExit: boolean
  }
}

// Interface for extracted events
export interface ExtractedEvent {
  event: string
  eventType: string
  eventDescription: string
  timestamp: string
  estimatedTime: boolean
  confidence: number
  relevanceScore: number // Legal relevance score 0.0-1.0
  pageReference?: string
  sourceDocumentId?: number | string
  docType?: string
  provider?: string
  providerSpecialty?: string
  department?: string
  facility?: string
}

// Options for medical chronology generation
export interface MedicalChronologyOptions {
  forceRegenerate?: boolean // Force regeneration of all events, ignoring existing ones
}

// Status of existing events for documents
export interface ExistingEventsStatus {
  documentsWithEvents: number[]
  documentsWithoutEvents: number[]
  eventsByDoc: Record<number, { total: number; processed: number }>
  totalExistingEvents: number
  totalProcessedEvents: number
}

// Document event interface for database compatibility
export interface DocumentEventDb {
  id: number
  binderId: string
  documentId: number | string
  pageRange: string
  event: string
  eventType: string
  eventDescription: string
  timestamp: Date
  estimatedTime: boolean
  rawExtractedData: any
  processed: boolean
  createdAt: Date
  updatedAt: Date
}

// Timeline event interface for report generation
export interface TimelineEvent {
  timestamp: string
  event: string
  eventType: string
  eventDescription: string
  documentId: number | string
  pageRange: string
  provider?: string
  providerSpecialty?: string
  department?: string
  facility?: string
}

// Provider information extracted from events
export interface ProviderInfo {
  provider?: string
  department?: string
}
