import { cn } from '@/lib/utils'
import Image from 'next/image'
import { Icons } from '@/components/elements/icons'
import { FeatureDisplay } from '@/components/elements/marketing/feature-display'
import { Testimonials } from '@/components/elements/marketing/testimonials'
import { Integrations } from '@/components/elements/marketing/integrations'

export default async function IndexPage() {
  const features = [
    {
      title: 'Intelligent Legal Assistant',
      heading: 'Your Smart Legal Ally',
      description:
        'An AI-driven assistant that streamlines your daily tasks by delivering precise insights, drafting support, and case-specific recommendations. It efficiently analyzes complex, even handwritten documents, ensuring no missed diagnoses or untreated symptoms, helping your clients achieve the best possible compensation.'
    },
    {
      title: 'Smart Document Analysis',
      heading: 'Unlock Insights with AI',
      description:
        'Master handwritten and complex document analysis with ease—unlock critical insights, extract key details, and simplify even the toughest legal and medical paperwork seamlessly.'
    },
    {
      title: 'Juror Evaluation',
      heading: 'Understand Your Jurors',
      description:
        'Gain deeper insights into juror behavior and profiles using AI-powered analysis. Evaluate potential biases, preferences, and patterns to build stronger trial strategies and improve case outcomes effectively.'
    },
    {
      title: 'Lightning-Fast Redaction',
      heading: 'Redact with Precision',
      description:
        'Redact sensitive information from legal and medical documents in seconds. With AI precision, ensure compliance, protect privacy, and streamline workflows without compromising accuracy or speed.'
    },
    {
      title: 'Case Evaluation',
      heading: 'Analyze Cases with Confidence',
      description:
        'Effortlessly evaluate personal injury and mass tort cases with AI-powered tools that analyze evidence, uncover key details, assess damages, and identify case strengths, helping you build stronger strategies for maximum compensation.'
    },
    {
      title: 'Affordable Cloud Storage',
      heading: 'Secure Storage, Anywhere Access',
      description:
        'Secure, scalable, and cost-effective cloud storage designed for personal injury and mass tort cases. Safely store client files, medical records, and legal documents with easy access and seamless collaboration from anywhere.'
    },
    {
      title: 'Demand Letter Drafting',
      heading: 'Draft Compelling Letters Instantly',
      description:
        'Automate demand letter drafting with precision and speed. Tailor compelling letters that highlight key facts, damages, and demands for personal injury and mass tort cases, ensuring maximum impact and better settlements.'
    },
    {
      title: 'Evidence Management',
      heading: 'Optimize Evidence Handling',
      description:
        'Organize, categorize, and retrieve exhibits effortlessly while adapting strategies to the complexities of mass tort dynamics. Ensure seamless evidence handling, enhanced collaboration, and optimized trial preparation tailored to the unique demands of each case.'
    }
  ]

  return (
    <>
      <section className="space-y-6 pb-8 pt-6 md:pb-12 md:pt-10">
        <div className="container flex max-w-[64rem] flex-col items-center gap-4 text-center">
          <Icons.logoWide className="xs:block lg:hidden" />
          <h1 className="font-heading text-3xl sm:text-5xl md:text-6xl lg:text-7xl">
            Purpose-built AI
            <br />
            for Personal Injury and
            <br /> Mass Tort Law Firms
          </h1>
          <p className="max-w-[42rem] leading-normal text-muted-foreground sm:text-xl sm:leading-8">
            Reliable, precise, and affordable
          </p>
        </div>
      </section>

      <section
        id="features"
        className="container space-y-6 bg-slate-50/50 py-8 xdark:bg-transparent md:pb-12 lg:pb-24"
      >
        <FeatureDisplay features={features} />
      </section>

      <section
        id="features"
        className="container space-y-16 bg-slate-50/50 py-8 xdark:bg-transparent md:py-12 lg:py-18 hidden md:block"
      >
        <div className="mx-auto max-w-[58rem] flex flex-col items-center space-y-4 text-center">
          <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-5xl">
            Supported By
          </h2>
        </div>
        <div className="mx-auto grid justify-center gap-4 sm:grid-cols-1 md:max-w-[64rem] md:grid-cols-3">
          {SupportedBy.map((support, index) => (
            <Image
              key={index}
              src={support.logo}
              alt={support.title}
              className={cn(
                'm-auto h-14 w-auto',
                support.invert &&
                  'xdark:invert xdark:saturate-100 xdark:brightness-0'
              )}
              width={300}
              height={300}
            />
          ))}
        </div>
      </section>

      <section className="space-y-6 pb-8 pt-6 md:pb-12 md:pt-10 lg:py-32">
        <div className="container lg:grid lg:grid-cols-2 items-center gap-4 text-center">
          <h2 className="font-heading text-center lg:text-left text-3xl lg:text-6xl">
            Advanced Legal AI Engineered to Solve High-Volume, Complex
            Litigation Challenges
          </h2>
          <span className="mt-6 max-w-[42rem] text-left leading-normal sm:text-xl sm:leading-8 space-y-3">
            <p>
              SmartCounsel’s AI-powered platform empowers lawyers to seamlessly
              interact with case data and obtain accurate, real-time insights.
              It is designed to:
            </p>
            <ul className="mt-4 space-y-2 ml-6 list-disc">
              <li>Analyze medical records and identify critical details.</li>
              <li>Highlight inconsistencies or gaps in evidence.</li>
              <li>
                Identify weaknesses in legal arguments and propose
                counterstrategies.
              </li>
            </ul>
          </span>
        </div>
      </section>

      <section
        id="features"
        className="container space-y-6 bg-slate-50/50 py-8 xdark:bg-transparent md:py-12 lg:py-24"
      >
        <div className="mx-auto max-w-[58rem] flex flex-col items-center space-y-4 text-center">
          <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl">
            Features
          </h2>
          <p className="max-w-[85%] leading-normal text-muted-foreground sm:text-lg sm:leading-7">
            SmartCounsel AI transforms legal workflows with advanced AI-driven
            solutions, enhancing efficiency, accuracy, and case outcomes. By
            automating complex tasks, it empowers law firms to focus on strategy
            and maximize client compensation.
          </p>
        </div>
        <div className="mx-auto grid justify-center gap-4 sm:grid-cols-2 md:max-w-[64rem]">
          <FeatureCards />
        </div>
      </section>

      <section className="container space-y-6 py-8 md:space-y-12 md:py-12 lg:py-24">
        <Testimonials />
      </section>

      <section className="container space-y-6 py-8 md:space-y-12 md:py-12 lg:py-24 bg-[#1c2519]">
        <Integrations />
      </section>
    </>
  )
}

const SupportedBy = [
  {
    title: 'AWS',
    invert: true,
    logo: '/marketing/logos/aws.png'
  },
  {
    title: 'Microsoft',
    invert: true,
    logo: '/marketing/logos/microsoft.png'
  },
  {
    title: 'OpenAI',
    invert: true,
    logo: '/marketing/logos/openaimono.png'
  }
]

const FeatureCards = () => {
  const features = [
    {
      title: 'Handwritten Document Analysis',
      description:
        'SmartCounsel’s AI deciphers handwritten records, such as medical notes or intake forms, ensuring no vital information is missed. It digitizes and organizes data for efficient case review and strategy planning.',
      icon: 'document-upload'
    },
    {
      title: 'Bulk Document Redaction',
      description:
        'SmartCounsel’s AI quickly redacts sensitive information from large volumes of documents, ensuring privacy compliance, saving time, and minimizing errors. It is perfect for mass tort and personal injury cases.',
      icon: 'document-compare'
    },
    {
      title: 'Juror Evaluation',
      description:
        "SmartCounsel’s AI analyzes jurors' social media posts to uncover biases, affiliations, or opinions. This helps attorneys make informed decisions during jury selection and efficiently eliminate unsuitable candidates.",
      icon: 'converse'
    },
    {
      title: 'Specific Information Retrieval',
      description:
        'SmartCounsel’s AI swiftly extracts and links key details from extensive document sets, enabling efficient analysis of medical records, timelines, and evidence for informed decisions in mass tort cases.',
      icon: 'research'
    }
  ]

  return (
    <>
      {features.map((feature, index) => (
        <div
          key={index}
          className="relative overflow-hidden rounded-lg border bg-background p-2"
        >
          <div className="flex h-[180px] flex-col justify-between rounded-md p-6">
            {/* <Icon/> */}
            <div className="space-y-2">
              <h3 className="font-bold">{feature.title}</h3>
              <p className="text-sm text-muted-foreground">
                {feature.description}
              </p>
            </div>
          </div>
        </div>
      ))}
    </>
  )
}
